CREATE TABLE IF NOT EXISTS public.auth_menu (
	id text NOT NULL, -- 菜单ID
	"name" text NOT NULL, -- 菜单
	url text NULL, -- 链接
	icon text NULL, -- 图标
	menu_level int4 NOT NULL, -- 菜单等级
	sort int4 NULL, -- 排序
	parent_id text NULL, -- 菜单父ID
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_menu IS '菜单表';
COMMENT ON COLUMN public.auth_menu.id IS '菜单ID';
COMMENT ON COLUMN public.auth_menu."name" IS '菜单';
COMMENT ON COLUMN public.auth_menu.url IS '链接';
COMMENT ON COLUMN public.auth_menu.icon IS '图标';
COMMENT ON COLUMN public.auth_menu.menu_level IS '菜单等级';
COMMENT ON COLUMN public.auth_menu.sort IS '排序';
COMMENT ON COLUMN public.auth_menu.parent_id IS '菜单父ID';
COMMENT ON COLUMN public.auth_menu.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_menu.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_menu.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_menu.update_by IS '更新用户';

INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES
	 ('workbench','{"zh-CN":"工作台","en-US":"Workbench"}','workbench','0',1,1,NULL,'2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('productManagement','{"zh-CN":"产品管理","en-US":"product Management"}','productManagement','0',1,3,NULL,'2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('knowledgeBase','{"zh-CN":"知识库","en-US":"Knowledge Base"}','knowledgeBase','0',1,9,NULL,'2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('billboard','{"zh-CN":"看板","en-US":"Billboard"}','billboard','0',1,12,NULL,'2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('LTCManagement','{"zh-CN":"LTC管理","en-US":"LTC Management"}','LTCManagement','0',1,6,NULL,'2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('myWorkbench','{"zh-CN":"我的工作台","en-US":"My Workbench"}','workbench/myWorkbench','0',2,2,'workbench','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('document','{"zh-CN":"文档","en-US":"Document"}','knowledgeBase/document','0',2,10,'knowledgeBase','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('FAQ','{"zh-CN":"FAQ","en-US":"FAQ"}','knowledgeBase/FAQ','0',2,11,'knowledgeBase','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('demandManagement','{"zh-CN":"需求管理","en-US":"Demand Management"}','productManagement/demandManagement','0',2,2,'productManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('selectionManagement','{"zh-CN":"选型管理","en-US":"Selection Management"}','productManagement/selectionManagement','0',2,3,'productManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_menu (id,"name",url,icon,menu_level,sort,parent_id,create_time,update_time,create_by,update_by) VALUES
	 ('costManagement','{"zh-CN":"成本管理","en-US":"Cost Management"}','productManagement/costManagement','0',2,4,'productManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('clueManagement','{"zh-CN":"线索管理","en-US":"Clue Management"}','LTCManagement/clueManagement','0',2,1,'LTCManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('productLibrary','{"zh-CN":"产品库","en-US":"Product Library"}','productManagement/productLibrary','0',2,1,'productManagement','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('businessManagement','{"zh-CN":"商机管理","en-US":"Business Management"}','LTCManagement/businessManagement','0',2,2,'LTCManagement','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('projectManagement','{"zh-CN":"项目管理","en-US":"Project Management"}','LTCManagement/projectManagement','0',2,3,'LTCManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('brandGuidanceProject','{"zh-CN":"项目品牌评分","en-US":"BrandGuidance Project"}','billboard/brandGuidanceProject','0',2,13,'billboard','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('brandGuidanceProduct','{"zh-CN":"产品品牌评分","en-US":"BrandGuidance Product"}','billboard/brandGuidanceProduct','0',2,14,'billboard','2025-03-20 10:44:59','2025-03-20 10:44:59','admin','admin'),
	 ('systemAccessStatistics', '{"zh-CN":"系统运营分析","en-US":"System Statistic"}', 'billboard/systemAccessStatistics', '0', 0, 15, 'billboard', '2025-02-25 14:46:58', '2025-02-25 14:46:58', 'admin', 'admin');


CREATE TABLE IF NOT EXISTS public.auth_permission (
	id text NOT NULL, -- 权限ID
	"name" text NOT NULL, -- 权限名称
	"type" int4 NOT NULL, -- 权限类型，0 菜单，1 操作
	permission_code text NOT NULL, -- 操作码
	menu_id text NULL, -- 菜单
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_permission IS '权限表';
COMMENT ON COLUMN public.auth_permission.id IS '权限ID';
COMMENT ON COLUMN public.auth_permission."name" IS '权限名称';
COMMENT ON COLUMN public.auth_permission."type" IS '权限类型，0 菜单，1 操作';
COMMENT ON COLUMN public.auth_permission.permission_code IS '操作码';
COMMENT ON COLUMN public.auth_permission.menu_id IS '菜单';
COMMENT ON COLUMN public.auth_permission.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_permission.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_permission.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_permission.update_by IS '更新用户';

INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('1','新增品牌',1,'product.brand.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('2','编辑品牌',1,'product.brand.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('3','删除品牌',1,'product.brand.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('4','新增分组',1,'product.group.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('5','编辑分组',1,'product.group.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('6','删除分组',1,'product.group.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('7','新增文档',1,'document.document.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('8','编辑文档',1,'document.document.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('9','删除文档',1,'document.document.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('10','新增物料',1,'product.material.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('11','编辑物料',1,'product.material.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('12','删除物料',1,'product.material.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('13','上下架及提交或取消变更操作',1,'product.material.process.operation',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('14','批量上下架及提交变更',1,'product.material.batch.operation',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('15','物料批量上架',1,'product.material.batch.submit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('16','新增FAQ',1,'system.faq.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('17','编辑FAQ',1,'system.faq.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('18','删除FAQ',1,'system.faq.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('19','工作台',0,'workbench','workbench','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('20','产品库',0,'productLibrary','productLibrary','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('21','产品管理',0,'productManagement','productManagement','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('22','我的工作台',0,'myWorkbench','myWorkbench','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('23','知识库',0,'knowledgeBase','knowledgeBase','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('24','物料查询',0,'materialQuery','materialQuery','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('25','FAQ',0,'FAQ','FAQ','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('26','文档',0,'document','document','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('27','更新产品小类关联用户',1,'product.category.update',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('30','项目库',0,'projectRepository','projectRepository','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('31','看板',0,'billboard','billboard','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('32','项目品牌评分',0,'brandGuidanceProject','brandGuidanceProject','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('33','产品品牌评分',0,'brandGuidanceProduct','brandGuidanceProduct','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('34','新增项目',1,'project.project.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('35','编辑项目',1,'project.project.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('36','删除项目',1,'project.project.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('37','品牌引导提交',1,'project.brandGuide.submit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('38','新增标书澄清',1,'project.biddingDocumentClarification.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('39','编辑标书澄清',1,'project.biddingDocumentClarification.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('40','删除标书澄清',1,'project.biddingDocumentClarification.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('41','关联物料',1,'project.material.associate',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('42','启动投标',1,'project.bidding.stage.start.bidding',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('43','更新招标文件',1,'project.bidding.stage.update.bidding.information',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('44','新增工程量清单',1,'project.bid.decomposition.bill.of.quantities.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('45','编辑工程量清单',1,'project.bid.decomposition.bill.of.quantities.edit',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('46','删除工程量清单',1,'project.bid.decomposition.bill.of.quantities.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('47','核心参数新增',1,'product.core.add',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('48','核心参数编辑',1,'product.core.update',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('49','核心参数删除',1,'product.core.delete',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('50','核心参数导入',1,'product.core.import',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('51','核心参数导出',1,'product.core.export',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('52','核心参数下载',1,'product.core.download',NULL,'2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('28','LTC管理',0,'LTCManagement','LTCManagement','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('29','项目管理',0,'projectManagement','projectManagement','2025-03-20 10:30:19','2025-03-20 10:30:19','admin','admin'),
	 ('53','品牌基础信息编辑',1,'product.brand.edit.base','','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('54','需求管理',0,'demandManagement','demandManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('55','选型管理',0,'selectionManagement','selectionManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('56','成本管理',0,'costManagement','costManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('57','商机管理',0,'businessManagement','businessManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('58','线索管理',0,'clueManagement','clueManagement','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('59','自定义产品分组排序',1,'product.group.custom.sort','','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('61','产品小类维护-产品小类人员配置-编辑',1,'productCategory.maintain.user.edit',NULL,'2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('60','产品小类维护-延保系数-编辑',1,'productCategory.maintain.extWarrantyFactor.edit',NULL,'2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('62','物料成本信息查看',1,'material.cost.info.view',NULL,'2025-03-28 20:27:52','2025-03-28 20:27:52','admin','admin'),
	 ('63','物料基本信息查看',1,'material.basic.info.view',NULL,'2025-03-28 20:27:52','2025-03-28 20:27:52','admin','admin'),
	 ('64','物料交期信息查看',1,'material.delivery.info.view',NULL,'2025-03-28 20:27:52','2025-03-28 20:27:52','admin','admin'),
	 ('65','产品库-品牌查看',1,'product.brand.view',NULL,'2025-03-28 20:27:52','2025-03-28 20:27:52','admin','admin'),
	 ('66','产品库-产品文档查看',1,'product.document.view',NULL,'2025-03-28 20:27:52','2025-03-28 20:27:52','admin','admin'),
	 ('67','产品管理-成本管理-招采成本-编辑',1,'procurement.cost.edit',NULL,'2025-04-10 16:36:39','2025-04-10 16:36:39','admin','admin'),
	 ('68','产品管理-成本管理-招采成本-成本拆分',1,'product.material.batch.update.cost',NULL,'2025-04-10 16:36:39','2025-04-10 16:36:39','admin','admin'),
	 ('69','需求管理-需求详情-新增选型单',1,'demandManagement.detail.add',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('70','需求管理-需求详情-编辑选型单',1,'demandManagement.detail.edit',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin');
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
	 ('71','需求管理-需求详情-删除选型单',1,'demandManagement.detail.delete',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('72','需求管理-需求详情-创建物料',1,'demandManagement.detail.createMaterial',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('73','需求管理-需求详情-创建物料-成本拆份',1,'demandManagement.detail.createMaterial.costSplitting',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('75','商机管理-选型需求',1,'selectionRequirements',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('76','需求管理-需求详情',1,'demandManagement.detail',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('77','需求管理-转化',1,'demandManagement.conversion',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('74','需求管理-成本总监编辑选型单',1,'demandManagement.cost.conversion',NULL,'2025-04-10 20:10:18','2025-04-10 20:10:18','admin','admin'),
	 ('80','产品管理-成本管理-招采成本-成本查看',1,'procurement.cost.view',NULL,'2025-04-15 16:13:01','2025-04-15 16:13:01','admin','admin'),
	 ('81','产品管理-成本管理-招采成本-物料成本查看',1,'product.material.cost.view',NULL,'2025-04-15 16:13:01','2025-04-15 16:13:01','admin','admin');
--权限点 产品信息配置(产品小类维护)-材料类别-编辑 产品信息配置(产品小类维护)-采购商务-编辑
INSERT INTO public.auth_permission (id,"name","type",permission_code,menu_id,create_time,update_time,create_by,update_by) VALUES
    ('82', '产品信息配置(产品小类维护)-材料类别-编辑', 1, 'productCategory.material.cat.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('83', '产品信息配置(产品小类维护)-采购商务-编辑', 1, 'productCategory.procurement.business.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('84', '物料成本变更权限码', 1, 'material.cost.info.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('85', '产品需求看板信息编辑权限码', 1, 'product.requirement.dashboard.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('86', '产品需求看板代码状态编辑权限码', 1, 'product.requirement.dashboard.code.status.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('87', '产品需求看板投标品牌编辑权限码', 1, 'product.requirement.dashboard.bid.brand.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('88', '产品需求看板招标时间编辑权限码', 1, 'product.requirement.dashboard.bid.time.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('89', '产品需求看板需求确认时间编辑权限码', 1, 'product.requirement.dashboard.demand.confirmation.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('90', '产品需求看板状态-资源模式-期望开标编辑权限码', 1, 'product.requirement.dashboard.status.resource.expect.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('91', '投标结论查看', 1, 'project.bidding.conclusion.view', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('92', '投标结论编辑', 1, 'project.bidding.conclusion.update', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('93', '售前售后交接信息填写', 1, 'project.handover.completed.add', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('94', '项目管理编辑', 1, 'project.item.update', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('95', '售前售后交接信息更新', 1, 'project.handover.completed.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('96', '工程交付信息更新', 1, 'project.delivery.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('97', '项目管理-遗留问题新增', 1, 'item.legacy.issues.add', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('98', '项目管理-遗留问题编辑', 1, 'item.legacy.issues.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('99', '项目管理-遗留问题删除', 1, 'item.legacy.issues.delete', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('100', '项目管理-文档新增', 1, 'item.document.add', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('101', '项目管理-文档修改', 1, 'item.document.edit', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('102', '项目管理-文档删除', 1, 'item.document.delete', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('103', '项目管理-文档查询', 1, 'item.document.view', NULL, to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('104', '产品库-物料规格书备注维护', 1, 'material.specification.remark.maintain', NULL,to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),'admin', 'admin');




CREATE TABLE IF NOT EXISTS public.auth_resource (
	id text NOT NULL, -- 资源ID
	entity_id text NOT NULL, -- 实体资源ID，关联产品、项目等
	parent_id text NULL, -- 父资源ID
	"type" int4 NOT NULL, -- 分类，产品或者分类等
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_resource IS '资源表';
COMMENT ON COLUMN public.auth_resource.id IS '资源ID';
COMMENT ON COLUMN public.auth_resource.entity_id IS '实体资源ID，关联产品、项目等';
COMMENT ON COLUMN public.auth_resource.parent_id IS '父资源ID';
COMMENT ON COLUMN public.auth_resource."type" IS '分类，产品或者分类等';
COMMENT ON COLUMN public.auth_resource.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_resource.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_resource.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_resource.update_by IS '更新用户';

INSERT INTO auth_resource
    (id,entity_id,parent_id,type,create_time,update_time,create_by,update_by)
    VALUES
    ('root','root',NULL,3,to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),'admin','admin');


CREATE TABLE IF NOT EXISTS public.auth_role (
	id text NOT NULL, -- 角色ID
	"name" text NOT NULL, -- 角色名称
	code text NOT NULL, -- 角色代码/编码，不可重复
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_role IS '角色表';
COMMENT ON COLUMN public.auth_role.id IS '角色ID';
COMMENT ON COLUMN public.auth_role."name" IS '角色名称';
COMMENT ON COLUMN public.auth_role.code IS '角色代码/编码，不可重复';
COMMENT ON COLUMN public.auth_role.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_role.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_role.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_role.update_by IS '更新用户';

INSERT INTO public.auth_role (id,"name",code,create_time,update_time,create_by,update_by) VALUES
	 ('53806c42-5806-4c51-81ff-15c31196228e','系统管理员','admin','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('70ef3496-9c4b-4f99-992d-4a40acea03aa','产品经理','product-manager','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('65b24875-6c22-417d-954c-a7d162b972e4','产品SE','product-se','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('84a5117f-e6c8-4583-b3f4-7fc105336004','成本总监','cost-director','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('cff28622-86d2-4048-8a4a-cab23a161009','商务总监','business-director','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('133c7aa1-080c-4231-ab75-f219abb5eeea','配置经理','configuration-manager','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('06a77708-8267-43d3-b66c-b69209c76937','物流总监','logistics-director','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('b7a13753-5628-4d37-89cb-2e76fcb8e20a','市场代表','market-representative','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','售后代表','after-sales-representative','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('9867bda5-2679-4561-a978-33cb5bafbcd0','品牌管理员','brand-manager','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system');
INSERT INTO public.auth_role (id,"name",code,create_time,update_time,create_by,update_by) VALUES
	 ('fd796cbc-45e5-4416-bedb-d8c70fa1acd2','工程总监','engineering-director','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('e5e2ae6a-c863-406c-8316-20aa72fb052f','项目管理员','project-manager','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('7ab75384-319d-4b6d-8827-adc55ab0539f','项目查看人','project-viewer','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('484da0ef-0140-4998-95f4-5386a8048d7c','项目评分查看人','project-score-viewer','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('3c7eb974-c394-41ef-b1f0-fd4344f7f896','交付TD','deliver-td','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('59b34b85-70f0-4598-b072-c44c0f8b050f','市场投标支持产品SE','market-bid-product-se','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('9d2351be-54bb-461c-bcef-269268794f2a','方案经理','scheme-se','2025-03-11 16:24:02','2025-03-11 16:24:02','system','system'),
	 ('1e9e7696-2708-461f-8120-183bfc7bf1b1','材料助理','material-assistant','2025-04-07 15:37:51','2025-04-07 15:37:51','system','system'),
	 ('1e9e7696-2708-461f-8120-2697cc159357','采购商务','procurement-business','2025-04-07 15:37:51','2025-04-07 15:37:51','system','system'),
	 ('1e9e7696-2708-461f-8120-1234567890aa','工作台角色','workbench-role','2025-04-11 13:27:32','2025-04-11 13:27:32','system','system'),
	 ('251ae6e3-2018-4740-8cd8-8eaa57de8141','项目PD','project-pd','2025-04-11 13:27:32','2025-04-11 13:27:32','system','system'),
	 ('sjfzrae6a-c863-406c-123456789aaabbbc','设计负责人','design-principal','2025-05-15 15:00:00','2025-05-15 15:00:00','system','system');


CREATE TABLE IF NOT EXISTS public.auth_role_permission (
	id text NOT NULL, -- 关联ID
	role_id text NOT NULL, -- 角色ID
	permission_id text NOT NULL, -- 权限ID
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_role_permission IS '角色权限关联表';
COMMENT ON COLUMN public.auth_role_permission.id IS '关联ID';
COMMENT ON COLUMN public.auth_role_permission.role_id IS '角色ID';
COMMENT ON COLUMN public.auth_role_permission.permission_id IS '权限ID';
COMMENT ON COLUMN public.auth_role_permission.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_role_permission.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_role_permission.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_role_permission.update_by IS '更新用户';

INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('1','9867bda5-2679-4561-a978-33cb5bafbcd0','1','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('2','9867bda5-2679-4561-a978-33cb5bafbcd0','2','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('3','9867bda5-2679-4561-a978-33cb5bafbcd0','3','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('4','70ef3496-9c4b-4f99-992d-4a40acea03aa','4','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('5','70ef3496-9c4b-4f99-992d-4a40acea03aa','5','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('6','70ef3496-9c4b-4f99-992d-4a40acea03aa','6','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('7','70ef3496-9c4b-4f99-992d-4a40acea03aa','7','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('8','70ef3496-9c4b-4f99-992d-4a40acea03aa','8','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('9','70ef3496-9c4b-4f99-992d-4a40acea03aa','9','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('10','70ef3496-9c4b-4f99-992d-4a40acea03aa','10','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('11','70ef3496-9c4b-4f99-992d-4a40acea03aa','11','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('12','70ef3496-9c4b-4f99-992d-4a40acea03aa','12','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('13','70ef3496-9c4b-4f99-992d-4a40acea03aa','13','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('14','70ef3496-9c4b-4f99-992d-4a40acea03aa','14','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('15','70ef3496-9c4b-4f99-992d-4a40acea03aa','15','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('16','70ef3496-9c4b-4f99-992d-4a40acea03aa','16','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('17','70ef3496-9c4b-4f99-992d-4a40acea03aa','17','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('18','70ef3496-9c4b-4f99-992d-4a40acea03aa','18','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('19','65b24875-6c22-417d-954c-a7d162b972e4','4','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('20','65b24875-6c22-417d-954c-a7d162b972e4','5','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('21','65b24875-6c22-417d-954c-a7d162b972e4','6','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('22','65b24875-6c22-417d-954c-a7d162b972e4','7','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('23','65b24875-6c22-417d-954c-a7d162b972e4','8','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('24','65b24875-6c22-417d-954c-a7d162b972e4','9','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('25','65b24875-6c22-417d-954c-a7d162b972e4','10','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('26','65b24875-6c22-417d-954c-a7d162b972e4','11','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('27','65b24875-6c22-417d-954c-a7d162b972e4','12','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('28','65b24875-6c22-417d-954c-a7d162b972e4','13','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('29','65b24875-6c22-417d-954c-a7d162b972e4','14','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('30','65b24875-6c22-417d-954c-a7d162b972e4','15','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('31','65b24875-6c22-417d-954c-a7d162b972e4','16','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('32','65b24875-6c22-417d-954c-a7d162b972e4','17','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('33','65b24875-6c22-417d-954c-a7d162b972e4','18','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('34','9867bda5-2679-4561-a978-33cb5bafbcd0','7','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('35','9867bda5-2679-4561-a978-33cb5bafbcd0','8','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('36','9867bda5-2679-4561-a978-33cb5bafbcd0','9','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('37','9867bda5-2679-4561-a978-33cb5bafbcd0','19','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('38','9867bda5-2679-4561-a978-33cb5bafbcd0','20','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('39','9867bda5-2679-4561-a978-33cb5bafbcd0','21','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('40','9867bda5-2679-4561-a978-33cb5bafbcd0','22','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('41','9867bda5-2679-4561-a978-33cb5bafbcd0','23','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('42','9867bda5-2679-4561-a978-33cb5bafbcd0','24','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('43','9867bda5-2679-4561-a978-33cb5bafbcd0','25','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('44','9867bda5-2679-4561-a978-33cb5bafbcd0','26','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('45','70ef3496-9c4b-4f99-992d-4a40acea03aa','19','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('46','70ef3496-9c4b-4f99-992d-4a40acea03aa','20','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('47','70ef3496-9c4b-4f99-992d-4a40acea03aa','21','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('48','70ef3496-9c4b-4f99-992d-4a40acea03aa','22','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('49','70ef3496-9c4b-4f99-992d-4a40acea03aa','23','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('50','70ef3496-9c4b-4f99-992d-4a40acea03aa','24','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('51','70ef3496-9c4b-4f99-992d-4a40acea03aa','25','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('52','70ef3496-9c4b-4f99-992d-4a40acea03aa','26','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('53','65b24875-6c22-417d-954c-a7d162b972e4','19','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('54','65b24875-6c22-417d-954c-a7d162b972e4','20','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('55','65b24875-6c22-417d-954c-a7d162b972e4','21','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('56','65b24875-6c22-417d-954c-a7d162b972e4','22','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('57','65b24875-6c22-417d-954c-a7d162b972e4','23','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('58','65b24875-6c22-417d-954c-a7d162b972e4','24','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('59','65b24875-6c22-417d-954c-a7d162b972e4','25','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('60','65b24875-6c22-417d-954c-a7d162b972e4','26','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('61','70ef3496-9c4b-4f99-992d-4a40acea03aa','27','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('62','e5e2ae6a-c863-406c-8316-20aa72fb052f','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('63','e5e2ae6a-c863-406c-8316-20aa72fb052f','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('64','e5e2ae6a-c863-406c-8316-20aa72fb052f','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('68','9d2351be-54bb-461c-bcef-269268794f2a','7','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('69','9d2351be-54bb-461c-bcef-269268794f2a','8','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('70','9d2351be-54bb-461c-bcef-269268794f2a','9','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('71','e5e2ae6a-c863-406c-8316-20aa72fb052f','34','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('72','e5e2ae6a-c863-406c-8316-20aa72fb052f','35','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('73','e5e2ae6a-c863-406c-8316-20aa72fb052f','36','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('74','9d2351be-54bb-461c-bcef-269268794f2a','35','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('75','9d2351be-54bb-461c-bcef-269268794f2a','36','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('76','9d2351be-54bb-461c-bcef-269268794f2a','37','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('77','484da0ef-0140-4998-95f4-5386a8048d7c','31','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('78','484da0ef-0140-4998-95f4-5386a8048d7c','32','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('79','484da0ef-0140-4998-95f4-5386a8048d7c','33','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('93','484da0ef-0140-4998-95f4-5386a8048d7c','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('94','484da0ef-0140-4998-95f4-5386a8048d7c','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('95','484da0ef-0140-4998-95f4-5386a8048d7c','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('80','9d2351be-54bb-461c-bcef-269268794f2a','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('81','9d2351be-54bb-461c-bcef-269268794f2a','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('82','9d2351be-54bb-461c-bcef-269268794f2a','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('83','3c7eb974-c394-41ef-b1f0-fd4344f7f896','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('84','3c7eb974-c394-41ef-b1f0-fd4344f7f896','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('96','3c7eb974-c394-41ef-b1f0-fd4344f7f896','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('85','84a5117f-e6c8-4583-b3f4-7fc105336004','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('86','84a5117f-e6c8-4583-b3f4-7fc105336004','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('97','84a5117f-e6c8-4583-b3f4-7fc105336004','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('87','cff28622-86d2-4048-8a4a-cab23a161009','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('88','cff28622-86d2-4048-8a4a-cab23a161009','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('98','cff28622-86d2-4048-8a4a-cab23a161009','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('89','06a77708-8267-43d3-b66c-b69209c76937','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('90','06a77708-8267-43d3-b66c-b69209c76937','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('99','06a77708-8267-43d3-b66c-b69209c76937','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('91','7ab75384-319d-4b6d-8827-adc55ab0539f','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('92','7ab75384-319d-4b6d-8827-adc55ab0539f','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('100','7ab75384-319d-4b6d-8827-adc55ab0539f','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('101','59b34b85-70f0-4598-b072-c44c0f8b050f','28','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('102','59b34b85-70f0-4598-b072-c44c0f8b050f','29','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('103','59b34b85-70f0-4598-b072-c44c0f8b050f','30','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('111','65b24875-6c22-417d-954c-a7d162b972e4','41','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('107','9d2351be-54bb-461c-bcef-269268794f2a','38','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('108','9d2351be-54bb-461c-bcef-269268794f2a','39','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('109','9d2351be-54bb-461c-bcef-269268794f2a','40','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('110','9d2351be-54bb-461c-bcef-269268794f2a','41','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('112','59b34b85-70f0-4598-b072-c44c0f8b050f','19','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('113','59b34b85-70f0-4598-b072-c44c0f8b050f','22','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('114','9d2351be-54bb-461c-bcef-269268794f2a','42','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('115','9d2351be-54bb-461c-bcef-269268794f2a','43','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('116','9d2351be-54bb-461c-bcef-269268794f2a','44','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('117','9d2351be-54bb-461c-bcef-269268794f2a','45','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('118','9d2351be-54bb-461c-bcef-269268794f2a','46','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('120','65b24875-6c22-417d-954c-a7d162b972e4','47','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('121','65b24875-6c22-417d-954c-a7d162b972e4','48','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('122','65b24875-6c22-417d-954c-a7d162b972e4','49','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('123','65b24875-6c22-417d-954c-a7d162b972e4','50','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('124','65b24875-6c22-417d-954c-a7d162b972e4','51','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('125','65b24875-6c22-417d-954c-a7d162b972e4','52','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('126','65b24875-6c22-417d-954c-a7d162b972e4','7','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('127','65b24875-6c22-417d-954c-a7d162b972e4','8','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('128','65b24875-6c22-417d-954c-a7d162b972e4','9','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('129','65b24875-6c22-417d-954c-a7d162b972e4','2','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('130','70ef3496-9c4b-4f99-992d-4a40acea03aa','47','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('131','70ef3496-9c4b-4f99-992d-4a40acea03aa','48','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('132','70ef3496-9c4b-4f99-992d-4a40acea03aa','49','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('133','70ef3496-9c4b-4f99-992d-4a40acea03aa','50','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('134','70ef3496-9c4b-4f99-992d-4a40acea03aa','51','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('105','59b34b85-70f0-4598-b072-c44c0f8b050f','39','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('106','59b34b85-70f0-4598-b072-c44c0f8b050f','40','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('135','70ef3496-9c4b-4f99-992d-4a40acea03aa','52','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('136','9867bda5-2679-4561-a978-33cb5bafbcd0','53','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('154','cff28622-86d2-4048-8a4a-cab23a161009','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('155','70ef3496-9c4b-4f99-992d-4a40acea03aa','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('156','65b24875-6c22-417d-954c-a7d162b972e4','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('157','84a5117f-e6c8-4583-b3f4-7fc105336004','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('158','133c7aa1-080c-4231-ab75-f219abb5eeea','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('159','06a77708-8267-43d3-b66c-b69209c76937','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('160','b7a13753-5628-4d37-89cb-2e76fcb8e20a','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('161','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('162','9867bda5-2679-4561-a978-33cb5bafbcd0','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('163','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('164','e5e2ae6a-c863-406c-8316-20aa72fb052f','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('165','7ab75384-319d-4b6d-8827-adc55ab0539f','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('166','484da0ef-0140-4998-95f4-5386a8048d7c','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('167','9d2351be-54bb-461c-bcef-269268794f2a','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('168','3c7eb974-c394-41ef-b1f0-fd4344f7f896','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('169','59b34b85-70f0-4598-b072-c44c0f8b050f','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('170','53806c42-5806-4c51-81ff-15c31196228e','55','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('171','cff28622-86d2-4048-8a4a-cab23a161009','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('172','70ef3496-9c4b-4f99-992d-4a40acea03aa','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('173','65b24875-6c22-417d-954c-a7d162b972e4','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('174','84a5117f-e6c8-4583-b3f4-7fc105336004','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('175','133c7aa1-080c-4231-ab75-f219abb5eeea','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('176','06a77708-8267-43d3-b66c-b69209c76937','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('177','b7a13753-5628-4d37-89cb-2e76fcb8e20a','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('178','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('179','9867bda5-2679-4561-a978-33cb5bafbcd0','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('180','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('181','e5e2ae6a-c863-406c-8316-20aa72fb052f','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('182','7ab75384-319d-4b6d-8827-adc55ab0539f','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('183','484da0ef-0140-4998-95f4-5386a8048d7c','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('184','9d2351be-54bb-461c-bcef-269268794f2a','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('185','3c7eb974-c394-41ef-b1f0-fd4344f7f896','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('186','59b34b85-70f0-4598-b072-c44c0f8b050f','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('187','53806c42-5806-4c51-81ff-15c31196228e','56','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('188','cff28622-86d2-4048-8a4a-cab23a161009','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('191','84a5117f-e6c8-4583-b3f4-7fc105336004','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('193','06a77708-8267-43d3-b66c-b69209c76937','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('199','7ab75384-319d-4b6d-8827-adc55ab0539f','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('200','484da0ef-0140-4998-95f4-5386a8048d7c','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('201','9d2351be-54bb-461c-bcef-269268794f2a','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('202','3c7eb974-c394-41ef-b1f0-fd4344f7f896','57','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('205','cff28622-86d2-4048-8a4a-cab23a161009','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('208','84a5117f-e6c8-4583-b3f4-7fc105336004','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('210','06a77708-8267-43d3-b66c-b69209c76937','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('216','7ab75384-319d-4b6d-8827-adc55ab0539f','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('217','484da0ef-0140-4998-95f4-5386a8048d7c','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('218','9d2351be-54bb-461c-bcef-269268794f2a','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('219','3c7eb974-c394-41ef-b1f0-fd4344f7f896','58','2025-03-11 16:24:02','2025-03-11 16:24:02','admin','admin'),
	 ('240','65b24875-6c22-417d-954c-a7d162b972e4','59','2025-03-25 11:24:02','2025-03-25 11:24:02','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('241','70ef3496-9c4b-4f99-992d-4a40acea03aa','59','2025-03-25 11:24:02','2025-03-25 11:24:02','admin','admin'),
	 ('242','70ef3496-9c4b-4f99-992d-4a40acea03aa','60','2025-03-26 11:35:55','2025-03-26 11:35:55','admin','admin'),
	 ('243','70ef3496-9c4b-4f99-992d-4a40acea03aa','61','2025-03-26 11:35:55','2025-03-26 11:35:55','admin','admin'),
	 ('244','65b24875-6c22-417d-954c-a7d162b972e4','60','2025-03-26 11:35:55','2025-03-26 11:35:55','admin','admin'),
	 ('245','65b24875-6c22-417d-954c-a7d162b972e4','27','2025-03-26 14:44:47','2025-03-26 14:44:47','admin','admin'),
	 ('246','e5e2ae6a-c863-406c-8316-20aa72fb052f','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('247','484da0ef-0140-4998-95f4-5386a8048d7c','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('248','9d2351be-54bb-461c-bcef-269268794f2a','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('249','3c7eb974-c394-41ef-b1f0-fd4344f7f896','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('250','84a5117f-e6c8-4583-b3f4-7fc105336004','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('251','cff28622-86d2-4048-8a4a-cab23a161009','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('252','06a77708-8267-43d3-b66c-b69209c76937','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('253','7ab75384-319d-4b6d-8827-adc55ab0539f','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('254','59b34b85-70f0-4598-b072-c44c0f8b050f','21','2025-03-26 15:01:11','2025-03-26 15:01:11','admin','admin'),
	 ('255','e5e2ae6a-c863-406c-8316-20aa72fb052f','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('256','e5e2ae6a-c863-406c-8316-20aa72fb052f','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('257','e5e2ae6a-c863-406c-8316-20aa72fb052f','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('258','e5e2ae6a-c863-406c-8316-20aa72fb052f','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('259','e5e2ae6a-c863-406c-8316-20aa72fb052f','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('260','e5e2ae6a-c863-406c-8316-20aa72fb052f','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('261','9d2351be-54bb-461c-bcef-269268794f2a','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('262','9d2351be-54bb-461c-bcef-269268794f2a','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('263','9d2351be-54bb-461c-bcef-269268794f2a','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('264','9d2351be-54bb-461c-bcef-269268794f2a','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('265','9d2351be-54bb-461c-bcef-269268794f2a','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('266','9d2351be-54bb-461c-bcef-269268794f2a','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('267','484da0ef-0140-4998-95f4-5386a8048d7c','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('268','484da0ef-0140-4998-95f4-5386a8048d7c','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('269','484da0ef-0140-4998-95f4-5386a8048d7c','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('270','484da0ef-0140-4998-95f4-5386a8048d7c','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('271','484da0ef-0140-4998-95f4-5386a8048d7c','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('272','484da0ef-0140-4998-95f4-5386a8048d7c','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('273','3c7eb974-c394-41ef-b1f0-fd4344f7f896','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('274','3c7eb974-c394-41ef-b1f0-fd4344f7f896','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('275','3c7eb974-c394-41ef-b1f0-fd4344f7f896','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('276','3c7eb974-c394-41ef-b1f0-fd4344f7f896','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('277','3c7eb974-c394-41ef-b1f0-fd4344f7f896','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('278','3c7eb974-c394-41ef-b1f0-fd4344f7f896','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('279','84a5117f-e6c8-4583-b3f4-7fc105336004','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('280','84a5117f-e6c8-4583-b3f4-7fc105336004','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('281','84a5117f-e6c8-4583-b3f4-7fc105336004','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('282','84a5117f-e6c8-4583-b3f4-7fc105336004','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('283','84a5117f-e6c8-4583-b3f4-7fc105336004','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('284','84a5117f-e6c8-4583-b3f4-7fc105336004','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('285','cff28622-86d2-4048-8a4a-cab23a161009','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('286','cff28622-86d2-4048-8a4a-cab23a161009','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('287','cff28622-86d2-4048-8a4a-cab23a161009','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('288','cff28622-86d2-4048-8a4a-cab23a161009','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('289','cff28622-86d2-4048-8a4a-cab23a161009','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('290','cff28622-86d2-4048-8a4a-cab23a161009','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('291','06a77708-8267-43d3-b66c-b69209c76937','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('292','06a77708-8267-43d3-b66c-b69209c76937','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('293','06a77708-8267-43d3-b66c-b69209c76937','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('294','06a77708-8267-43d3-b66c-b69209c76937','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('295','06a77708-8267-43d3-b66c-b69209c76937','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('296','06a77708-8267-43d3-b66c-b69209c76937','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('297','7ab75384-319d-4b6d-8827-adc55ab0539f','19','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('298','7ab75384-319d-4b6d-8827-adc55ab0539f','20','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('299','7ab75384-319d-4b6d-8827-adc55ab0539f','22','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('300','7ab75384-319d-4b6d-8827-adc55ab0539f','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('301','7ab75384-319d-4b6d-8827-adc55ab0539f','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('302','7ab75384-319d-4b6d-8827-adc55ab0539f','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('303','59b34b85-70f0-4598-b072-c44c0f8b050f','23','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('304','59b34b85-70f0-4598-b072-c44c0f8b050f','25','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('305','59b34b85-70f0-4598-b072-c44c0f8b050f','26','2025-03-26 22:12:16','2025-03-26 22:12:16','admin','admin'),
	 ('306','59b34b85-70f0-4598-b072-c44c0f8b050f','57','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('307','59b34b85-70f0-4598-b072-c44c0f8b050f','58','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('308','65b24875-6c22-417d-954c-a7d162b972e4','57','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('309','65b24875-6c22-417d-954c-a7d162b972e4','58','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('310','133c7aa1-080c-4231-ab75-f219abb5eeea','19','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('311','133c7aa1-080c-4231-ab75-f219abb5eeea','20','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('312','133c7aa1-080c-4231-ab75-f219abb5eeea','21','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('313','133c7aa1-080c-4231-ab75-f219abb5eeea','22','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('314','133c7aa1-080c-4231-ab75-f219abb5eeea','23','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('315','133c7aa1-080c-4231-ab75-f219abb5eeea','25','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('316','133c7aa1-080c-4231-ab75-f219abb5eeea','26','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('317','b7a13753-5628-4d37-89cb-2e76fcb8e20a','19','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('318','b7a13753-5628-4d37-89cb-2e76fcb8e20a','20','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('319','b7a13753-5628-4d37-89cb-2e76fcb8e20a','21','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('320','b7a13753-5628-4d37-89cb-2e76fcb8e20a','22','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('321','b7a13753-5628-4d37-89cb-2e76fcb8e20a','23','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('322','b7a13753-5628-4d37-89cb-2e76fcb8e20a','25','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('323','b7a13753-5628-4d37-89cb-2e76fcb8e20a','26','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('324','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','19','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('325','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','20','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('326','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','21','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('327','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','22','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('328','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','23','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('329','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','25','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('330','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','26','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('331','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','19','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('332','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','20','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('333','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','21','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('334','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','22','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('335','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','23','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('336','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','25','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('337','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','26','2025-03-28 10:44:06','2025-03-28 10:44:06','admin','admin'),
	 ('338','70ef3496-9c4b-4f99-992d-4a40acea03aa','62','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('339','65b24875-6c22-417d-954c-a7d162b972e4','62','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('340','84a5117f-e6c8-4583-b3f4-7fc105336004','62','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('341','70ef3496-9c4b-4f99-992d-4a40acea03aa','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('342','65b24875-6c22-417d-954c-a7d162b972e4','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('343','84a5117f-e6c8-4583-b3f4-7fc105336004','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('344','cff28622-86d2-4048-8a4a-cab23a161009','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('345','133c7aa1-080c-4231-ab75-f219abb5eeea','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('346','b7a13753-5628-4d37-89cb-2e76fcb8e20a','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('347','aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('348','fd796cbc-45e5-4416-bedb-d8c70fa1acd2','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('349','06a77708-8267-43d3-b66c-b69209c76937','63','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('350','70ef3496-9c4b-4f99-992d-4a40acea03aa','64','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('351','65b24875-6c22-417d-954c-a7d162b972e4','64','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('352','84a5117f-e6c8-4583-b3f4-7fc105336004','64','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('353','06a77708-8267-43d3-b66c-b69209c76937','64','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('354','70ef3496-9c4b-4f99-992d-4a40acea03aa','65','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('355','65b24875-6c22-417d-954c-a7d162b972e4','65','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('356','70ef3496-9c4b-4f99-992d-4a40acea03aa','66','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('357','65b24875-6c22-417d-954c-a7d162b972e4','66','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('358','9867bda5-2679-4561-a978-33cb5bafbcd0','66','2025-03-28 20:38:32','2025-03-28 20:38:32','admin','admin'),
	 ('400','65b24875-6c22-417d-954c-a7d162b972e4','28','2025-03-29 22:57:33','2025-03-29 22:57:33','admin','admin'),
	 ('401','59b34b85-70f0-4598-b072-c44c0f8b050f','28','2025-03-29 22:57:33','2025-03-29 22:57:33','admin','admin');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('402','84a5117f-e6c8-4583-b3f4-7fc105336004','67','2025-04-10 16:36:39','2025-04-10 16:36:39','admin','admin'),
	 ('403','84a5117f-e6c8-4583-b3f4-7fc105336004','68','2025-04-10 16:36:39','2025-04-10 16:36:39','admin','admin'),
	 ('140','70ef3496-9c4b-4f99-992d-4a40acea03aa','54','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('141','65b24875-6c22-417d-954c-a7d162b972e4','54','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('142','84a5117f-e6c8-4583-b3f4-7fc105336004','54','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('143','1e9e7696-2708-461f-8120-183bfc7bf1b1','54','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('407','65b24875-6c22-417d-954c-a7d162b972e4','77','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('408','84a5117f-e6c8-4583-b3f4-7fc105336004','74','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('409','70ef3496-9c4b-4f99-992d-4a40acea03aa','69','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('410','65b24875-6c22-417d-954c-a7d162b972e4','69','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('411','70ef3496-9c4b-4f99-992d-4a40acea03aa','70','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('412','65b24875-6c22-417d-954c-a7d162b972e4','70','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('413','70ef3496-9c4b-4f99-992d-4a40acea03aa','71','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('414','65b24875-6c22-417d-954c-a7d162b972e4','71','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('415','70ef3496-9c4b-4f99-992d-4a40acea03aa','72','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('416','65b24875-6c22-417d-954c-a7d162b972e4','72','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('417','84a5117f-e6c8-4583-b3f4-7fc105336004','72','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('418','84a5117f-e6c8-4583-b3f4-7fc105336004','73','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('419','84a5117f-e6c8-4583-b3f4-7fc105336004','75','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('420','70ef3496-9c4b-4f99-992d-4a40acea03aa','76','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system');
INSERT INTO public.auth_role_permission (id,role_id,permission_id,create_time,update_time,create_by,update_by) VALUES
	 ('421','65b24875-6c22-417d-954c-a7d162b972e4','76','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('422','84a5117f-e6c8-4583-b3f4-7fc105336004','76','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('423','1e9e7696-2708-461f-8120-183bfc7bf1b1','76','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('404','1e9e7696-2708-461f-8120-1234567890aa','19','2025-04-11 13:27:38','2025-04-11 13:27:38','admin','admin'),
	 ('405','1e9e7696-2708-461f-8120-1234567890aa','22','2025-04-11 13:27:38','2025-04-11 13:27:38','admin','admin'),
	 ('406','70ef3496-9c4b-4f99-992d-4a40acea03aa','69','2025-04-10 20:21:18','2025-04-10 20:21:18','system','system'),
	 ('424','84a5117f-e6c8-4583-b3f4-7fc105336004','80','2025-04-15 16:14:10','2025-04-15 16:14:10','admin','admin'),
	 ('425','84a5117f-e6c8-4583-b3f4-7fc105336004','81','2025-04-15 16:14:10','2025-04-15 16:14:10','admin','admin'),
	 ('104','59b34b85-70f0-4598-b072-c44c0f8b050f','38','2025-03-20 10:43:18','2025-03-20 10:43:18','admin','admin'),
	 ('426','1e9e7696-2708-461f-8120-183bfc7bf1b1','21','2025-04-18  09:06:00','2025-04-18  09:06:00','admin','admin'),
     ('427', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '19', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
     ('428', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '22', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');


    -- 2025-04-21 角色权限点 产品经理 产品信息配置(产品小类维护)-材料类别-编辑 产品信息配置(产品小类维护)-采购商务-编辑
INSERT INTO auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)VALUES
    ('429', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '82', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('430', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '83', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 2025-04-21 角色权限点 产品SE 产品信息配置(产品小类维护)-材料类别-编辑 产品信息配置(产品小类维护)-采购商务-编辑
    ('431', '65b24875-6c22-417d-954c-a7d162b972e4', '82', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('432', '65b24875-6c22-417d-954c-a7d162b972e4', '83', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('433', '84a5117f-e6c8-4583-b3f4-7fc105336004', '84', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('434', '9d2351be-54bb-461c-bcef-269268794f2a', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('435', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('436', '65b24875-6c22-417d-954c-a7d162b972e4', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('437', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '85', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    -- 2025-04-21 配置产品需求看板对应权限-编辑
INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES
    -- 2025-04-28 添加产品SE或材料助理 产品需求看板 代码状态编辑权限
    ('438', '65b24875-6c22-417d-954c-a7d162b972e4', '86', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('439', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '86', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 2025-04-28 添加产品SE 产品需求看板 产品需求看板投标品牌编辑权限
    ('440', '65b24875-6c22-417d-954c-a7d162b972e4', '87', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 2025-04-28 添加方产品经理、产品SE和材料助理的 产品需求看板 产品需求看板招标时间编辑权限
    ('441', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '88', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('442', '65b24875-6c22-417d-954c-a7d162b972e4', '88', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('443', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '88', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 2025-04-28 添加方案经理或产品SE 产品需求看板 需求确认时间编辑权限
    ('444', '9d2351be-54bb-461c-bcef-269268794f2a', '89', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('445', '65b24875-6c22-417d-954c-a7d162b972e4', '89', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    -- 2025-04-28 添加方案经理 产品需求看板 产品需求看板状态-资源模式-期望开标编辑权限
    ('446', '9d2351be-54bb-461c-bcef-269268794f2a', '90', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');
        -- 2025-05-08 添加方案经理 产品需求看板 产品需求看板状态-资源模式-期望开标编辑权限
    INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
    ('447', '9d2351be-54bb-461c-bcef-269268794f2a', '91', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('448', '9d2351be-54bb-461c-bcef-269268794f2a', '92', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('449', '9d2351be-54bb-461c-bcef-269268794f2a', '93', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('450', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '94', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    --2025-05-14 配置交付TD 售前售后交接信息更新、工程交付信息更新权限码
    ('451', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '95', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('452', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '96', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

    --2025-05-15 配置交付TD、设计负责人在项目管理中的部分按钮权限码
    INSERT INTO public.auth_role_permission
    (id, role_id, permission_id, create_time, update_time, create_by, update_by)
    VALUES
    ('453', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '97', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('454', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '98', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('455', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '99', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('456', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '100', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('457', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '101', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('458', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '102', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('459', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '103', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),  'admin', 'admin'),
    ('460', 'sjfzrae6a-c863-406c-123456789aaabbbc', '100', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),  'admin', 'admin'),
    ('461', 'sjfzrae6a-c863-406c-123456789aaabbbc', '101', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),  'admin', 'admin'),
    ('462', 'sjfzrae6a-c863-406c-123456789aaabbbc', '102', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('463', 'sjfzrae6a-c863-406c-123456789aaabbbc', '103', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('464', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '104', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('465', '65b24875-6c22-417d-954c-a7d162b972e4', '104', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('467', '1e9e7696-2708-461f-8120-1234567890aa', '23', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('468', '1e9e7696-2708-461f-8120-1234567890aa', '20', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('469', '1e9e7696-2708-461f-8120-1234567890aa', '21', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('470', '1e9e7696-2708-461f-8120-1234567890aa', '25', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
    ('471', '1e9e7696-2708-461f-8120-1234567890aa', '26', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

CREATE TABLE IF NOT EXISTS public.auth_user (
	id text NOT NULL, -- 用户ID
	"name" text NOT NULL, -- 用户名
	email text NOT NULL, -- 邮箱
	employee_id text NOT NULL, -- 工号
	phone_number text NULL, -- 手机号
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_user IS '用户表';
COMMENT ON COLUMN public.auth_user.id IS '用户ID';
COMMENT ON COLUMN public.auth_user."name" IS '用户名';
COMMENT ON COLUMN public.auth_user.email IS '邮箱';
COMMENT ON COLUMN public.auth_user.employee_id IS '工号';
COMMENT ON COLUMN public.auth_user.phone_number IS '手机号';
COMMENT ON COLUMN public.auth_user.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_user.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_user.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_user.update_by IS '更新用户';


CREATE TABLE IF NOT EXISTS public.auth_user_role_resource (
	id text NOT NULL, -- 关联ID
	user_id text NOT NULL, -- 用户ID
	role_id text NOT NULL, -- 角色ID
	resource_id text NOT NULL, -- 资源ID，对应的是auth_resource表中的ID
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_user_role_resource IS '用户角色资源关联表';
COMMENT ON COLUMN public.auth_user_role_resource.id IS '关联ID';
COMMENT ON COLUMN public.auth_user_role_resource.user_id IS '用户ID';
COMMENT ON COLUMN public.auth_user_role_resource.role_id IS '角色ID';
COMMENT ON COLUMN public.auth_user_role_resource.resource_id IS '资源ID，对应的是auth_resource表中的ID';
COMMENT ON COLUMN public.auth_user_role_resource.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_user_role_resource.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_user_role_resource.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_user_role_resource.update_by IS '更新用户';


CREATE TABLE IF NOT EXISTS public.faq (
	id text NOT NULL, -- 唯一标识符
	"type" int4 NOT NULL, -- 类型，1 产品小类，2 其他...
	resource_id text NOT NULL, -- 资源ID
	question text NOT NULL, -- 问题
	answer text NOT NULL, -- 答案
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建者ID
	update_by text NOT NULL, -- 更新者ID
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.faq IS 'FAQ 表';
COMMENT ON COLUMN public.faq.id IS '唯一标识符';
COMMENT ON COLUMN public.faq."type" IS '类型，1 产品小类，2 其他...';
COMMENT ON COLUMN public.faq.resource_id IS '资源ID';
COMMENT ON COLUMN public.faq.question IS '问题';
COMMENT ON COLUMN public.faq.answer IS '答案';
COMMENT ON COLUMN public.faq.create_time IS '创建时间';
COMMENT ON COLUMN public.faq.update_time IS '更新时间';
COMMENT ON COLUMN public.faq.create_by IS '创建者ID';
COMMENT ON COLUMN public.faq.update_by IS '更新者ID';


CREATE TABLE IF NOT EXISTS public.msg_delivery (
	delivery_id varchar(100) NOT NULL, -- 分发ID
	log_id varchar(100) NOT NULL, -- 消息ID
	user_id varchar(500) NOT NULL, -- 通知人
	send_type int2 NOT NULL, -- 分发类型(1:站内通知;2:icenter; 4:邮件通知;)
	contact text NULL, -- 联系方式(Email地址等，站内通知使用用户id不用填)
	msg_status int2 NOT NULL, -- 消息状态(0:未处理；1：已读取(或成功发送); 其它：错误码)
	error_msg varchar(500) NULL, -- 错误详情
	send_time timestamp NULL, -- 发送(或读取)时间
	create_time timestamp NULL, -- 创建时间
	update_time timestamp NULL, -- 更新时间
	PRIMARY KEY (delivery_id)
);
COMMENT ON TABLE public.msg_delivery IS '消息分发';
COMMENT ON COLUMN public.msg_delivery.delivery_id IS '分发ID';
COMMENT ON COLUMN public.msg_delivery.log_id IS '消息ID';
COMMENT ON COLUMN public.msg_delivery.user_id IS '通知人';
COMMENT ON COLUMN public.msg_delivery.send_type IS '分发类型(1:站内通知;2:icenter; 4:邮件通知;)';
COMMENT ON COLUMN public.msg_delivery.contact IS '联系方式(Email地址等，站内通知使用用户id不用填)';
COMMENT ON COLUMN public.msg_delivery.msg_status IS '消息状态(0:未处理；1：已读取(或成功发送); 其它：错误码)';
COMMENT ON COLUMN public.msg_delivery.error_msg IS '错误详情';
COMMENT ON COLUMN public.msg_delivery.send_time IS '发送(或读取)时间';
COMMENT ON COLUMN public.msg_delivery.create_time IS '创建时间';
COMMENT ON COLUMN public.msg_delivery.update_time IS '更新时间';

CREATE TABLE IF NOT EXISTS public.msg_log (
	log_id varchar(100) NOT NULL, -- 消息日志ID
	msg_name varchar(200) NOT NULL, -- 消息标题
	msg_content text NULL, -- 消息具体信息
	msg_type int2 NOT NULL, -- 消息类型(0:其他;1:任务;2:与我有关;)
	create_time timestamp NOT NULL, -- 入库时间
	notify_type int2 NOT NULL, -- 通知方式比特位(1:站内通知;2:icenter; 4:邮件通知;)
	notify_users varchar(500) NULL, -- 通知人(多个用英文逗号分隔)
	link varchar(300) NULL, -- 链接地址
	PRIMARY KEY (log_id)
);
COMMENT ON TABLE public.msg_log IS '消息日志';
COMMENT ON COLUMN public.msg_log.log_id IS '消息日志ID';
COMMENT ON COLUMN public.msg_log.msg_name IS '消息标题';
COMMENT ON COLUMN public.msg_log.msg_content IS '消息具体信息';
COMMENT ON COLUMN public.msg_log.msg_type IS '消息类型(0:其他;1:任务;2:与我有关;)';
COMMENT ON COLUMN public.msg_log.create_time IS '入库时间';
COMMENT ON COLUMN public.msg_log.notify_type IS '通知方式比特位(1:站内通知;2:icenter; 4:邮件通知;)';
COMMENT ON COLUMN public.msg_log.notify_users IS '通知人(多个用英文逗号分隔)';
COMMENT ON COLUMN public.msg_log.link IS '链接地址';


--- 2025-05-23
CREATE TABLE IF NOT EXISTS public.auth_department_role_resource (
	id text NOT NULL, -- 关联ID
	department_id text NOT NULL, -- 部门id
	role_id text NOT NULL, -- 角色ID
	resource_id text NOT NULL, -- 资源ID，对应的是auth_resource表中的ID
	create_time text NOT NULL, -- 创建时间
	update_time text NOT NULL, -- 更新时间
	create_by text NOT NULL, -- 创建用户
	update_by text NOT NULL, -- 更新用户
	PRIMARY KEY (id)
);
COMMENT ON TABLE public.auth_department_role_resource IS '部门角色资源关联表';
COMMENT ON COLUMN public.auth_department_role_resource.id IS '关联ID';
COMMENT ON COLUMN public.auth_department_role_resource.department_id IS ' 部门id';
COMMENT ON COLUMN public.auth_department_role_resource.role_id IS '角色ID';
COMMENT ON COLUMN public.auth_department_role_resource.resource_id IS '资源ID，对应的是auth_resource表中的ID';
COMMENT ON COLUMN public.auth_department_role_resource.create_time IS '创建时间';
COMMENT ON COLUMN public.auth_department_role_resource.update_time IS '更新时间';
COMMENT ON COLUMN public.auth_department_role_resource.create_by IS '创建用户';
COMMENT ON COLUMN public.auth_department_role_resource.update_by IS '更新用户';

    -- 部门表
    DROP TABLE IF EXISTS auth_dept;
    CREATE TABLE auth_dept (
      dept_id           VARCHAR(100)          NOT NULL PRIMARY KEY, -- 部门id
      parent_id         VARCHAR(100)          DEFAULT '0',             -- 父部门id
      dept_name         VARCHAR(50)     DEFAULT '',            -- 部门名称
      dept_level        INTEGER         DEFAULT 1,             -- 部门所在层级
      order_num         INTEGER         DEFAULT 0,             -- 显示顺序
      dept_path         text            DEFAULT '',            -- 组织路径
      dept_path_id      text            DEFAULT '',            -- 组织路径id
      leader            text            DEFAULT null,          -- 负责人
      remark            text            DEFAULT null,          -- 备注
      create_by         VARCHAR(64)     DEFAULT '',            -- 创建者
      create_time       VARCHAR(64)     default to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'),    -- 创建时间
      update_by         VARCHAR(64)     DEFAULT '',            -- 更新者
      update_time       VARCHAR(64)     default to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS')     -- 更新时间
    );

    COMMENT ON COLUMN auth_dept.dept_id IS '部门id';
    COMMENT ON COLUMN auth_dept.parent_id IS '父部门id';
    COMMENT ON COLUMN auth_dept.dept_name IS '部门名称';
    COMMENT ON COLUMN auth_dept.dept_level IS '部门所在层级';
    COMMENT ON COLUMN auth_dept.order_num IS '显示顺序';
    COMMENT ON COLUMN auth_dept.dept_path IS '组织路径';
    COMMENT ON COLUMN auth_dept.dept_path_id IS '组织路径id';
    COMMENT ON COLUMN auth_dept.leader IS '负责人';
    COMMENT ON COLUMN auth_dept.remark IS '备注';
    COMMENT ON COLUMN auth_dept.create_by IS '创建者';
    COMMENT ON COLUMN auth_dept.create_time IS '创建时间';
    COMMENT ON COLUMN auth_dept.update_by IS '更新者';
    COMMENT ON COLUMN auth_dept.update_time IS '更新时间';


    -- 部门人员关系表
DROP TABLE IF EXISTS auth_dept_user_relation;

CREATE TABLE auth_dept_user_relation (
    id           VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    dept_id      VARCHAR(100) NOT NULL,             -- 部门id
    user_id      VARCHAR(100) NOT NULL,             -- 用户id
    UNIQUE (dept_id, user_id)                       -- 添加组合唯一约束
);

-- 注释部分保持不变
COMMENT ON COLUMN auth_dept_user_relation.id IS '主键';
COMMENT ON COLUMN auth_dept_user_relation.dept_id IS '部门id';
COMMENT ON COLUMN auth_dept_user_relation.user_id IS '用户id';





INSERT INTO auth_permission (id, name, type, permission_code, create_time, update_time, create_by, update_by, menu_id)
VALUES (105, '系统运营分析', 0, 'systemAccessStatistics', '2025-02-08 09:15:01', '2025-02-08 09:15:01', 'admin', 'admin', 'systemAccessStatistics');

-- 搜索统计日表
CREATE TABLE IF NOT EXISTS search_stat_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE search_stat_day IS '搜索统计日表';
COMMENT ON COLUMN search_stat_day.id IS '主表id';
COMMENT ON COLUMN search_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN search_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_day.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_day.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_day.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_day.faq_num IS 'FAQ次数';
COMMENT ON COLUMN search_stat_day.create_time IS '记录时间';

-- 搜索统计周表
CREATE TABLE IF NOT EXISTS search_stat_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL
);

COMMENT ON TABLE search_stat_week IS '搜索统计周表';
COMMENT ON COLUMN search_stat_week.id IS '主表id';
COMMENT ON COLUMN search_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN search_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_week.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_week.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_week.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_week.faq_num IS 'FAQ次数';

-- 搜索统计月表
CREATE TABLE IF NOT EXISTS search_stat_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL
);

COMMENT ON TABLE search_stat_month IS '搜索统计月表';
COMMENT ON COLUMN search_stat_month.id IS '主表id';
COMMENT ON COLUMN search_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN search_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_month.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_month.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_month.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_month.faq_num IS 'FAQ次数';

-- 搜索统计年表
CREATE TABLE IF NOT EXISTS search_stat_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    file_num TEXT NOT NULL,
    faq_num TEXT NOT NULL
);

COMMENT ON TABLE search_stat_year IS '搜索统计年表';
COMMENT ON COLUMN search_stat_year.id IS '主表id';
COMMENT ON COLUMN search_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN search_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN search_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN search_stat_year.product_category_id IS '产品小类id';
COMMENT ON COLUMN search_stat_year.material_num IS '物料次数';
COMMENT ON COLUMN search_stat_year.file_num IS '文档次数';
COMMENT ON COLUMN search_stat_year.faq_num IS 'FAQ次数';

-- 智能体统计日表
CREATE TABLE IF NOT EXISTS intelligence_stat_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_day IS '智能体统计日表';
COMMENT ON COLUMN intelligence_stat_day.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN intelligence_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_day.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_day.bid_num IS '标书分析智能体次数';
COMMENT ON COLUMN intelligence_stat_day.create_time IS '记录时间';

-- 智能体统计周表
CREATE TABLE IF NOT EXISTS intelligence_stat_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_week IS '智能体统计周表';
COMMENT ON COLUMN intelligence_stat_week.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN intelligence_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_week.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_week.bid_num IS '标书分析智能体次数';

-- 智能体统计月表
CREATE TABLE IF NOT EXISTS intelligence_stat_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_month IS '智能体统计月表';
COMMENT ON COLUMN intelligence_stat_month.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN intelligence_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_month.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_month.bid_num IS '标书分析智能体次数';

-- 智能体统计年表
CREATE TABLE IF NOT EXISTS intelligence_stat_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    material_num TEXT NOT NULL,
    bid_num TEXT NOT NULL
);

COMMENT ON TABLE intelligence_stat_year IS '智能体统计年表';
COMMENT ON COLUMN intelligence_stat_year.id IS '主表id';
COMMENT ON COLUMN intelligence_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN intelligence_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN intelligence_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN intelligence_stat_year.material_num IS '物料智能体次数';
COMMENT ON COLUMN intelligence_stat_year.bid_num IS '标书分析智能体次数';





INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
('480', '53806c42-5806-4c51-81ff-15c31196228e', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('481', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('482', '65b24875-6c22-417d-954c-a7d162b972e4', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('483', '84a5117f-e6c8-4583-b3f4-7fc105336004', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('484', 'cff28622-86d2-4048-8a4a-cab23a161009', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('485', '133c7aa1-080c-4231-ab75-f219abb5eeea', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('486', '06a77708-8267-43d3-b66c-b69209c76937', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('487', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('488', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('489', '9867bda5-2679-4561-a978-33cb5bafbcd0', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('490', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('491', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('492', '7ab75384-319d-4b6d-8827-adc55ab0539f', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('493', '484da0ef-0140-4998-95f4-5386a8048d7c', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('494', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('495', '59b34b85-70f0-4598-b072-c44c0f8b050f', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('496', '9d2351be-54bb-461c-bcef-269268794f2a', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('497', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('498', '1e9e7696-2708-461f-8120-2697cc159357', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('499', '1e9e7696-2708-461f-8120-1234567890aa', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('500', '251ae6e3-2018-4740-8cd8-8eaa57de8141', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('501', 'sjfzrae6a-c863-406c-123456789aaabbbc', '105', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');


INSERT INTO public.auth_role_permission (id, role_id, permission_id, create_time, update_time, create_by, update_by) VALUES
('502', '53806c42-5806-4c51-81ff-15c31196228e', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('503', '70ef3496-9c4b-4f99-992d-4a40acea03aa', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('504', '65b24875-6c22-417d-954c-a7d162b972e4', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('505', '84a5117f-e6c8-4583-b3f4-7fc105336004', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('506', 'cff28622-86d2-4048-8a4a-cab23a161009', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('507', '133c7aa1-080c-4231-ab75-f219abb5eeea', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('508', '06a77708-8267-43d3-b66c-b69209c76937', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('509', 'b7a13753-5628-4d37-89cb-2e76fcb8e20a', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('510', 'aecf76ed-3dc8-48a7-adf6-d488b9c1c5a8', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('511', '9867bda5-2679-4561-a978-33cb5bafbcd0', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('512', 'fd796cbc-45e5-4416-bedb-d8c70fa1acd2', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('513', 'e5e2ae6a-c863-406c-8316-20aa72fb052f', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('514', '7ab75384-319d-4b6d-8827-adc55ab0539f', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('515', '484da0ef-0140-4998-95f4-5386a8048d7c', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('516', '3c7eb974-c394-41ef-b1f0-fd4344f7f896', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('517', '59b34b85-70f0-4598-b072-c44c0f8b050f', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('518', '9d2351be-54bb-461c-bcef-269268794f2a', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('519', '1e9e7696-2708-461f-8120-183bfc7bf1b1', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('520', '1e9e7696-2708-461f-8120-2697cc159357', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('521', '1e9e7696-2708-461f-8120-1234567890aa', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('522', '251ae6e3-2018-4740-8cd8-8eaa57de8141', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin'),
('523', 'sjfzrae6a-c863-406c-123456789aaabbbc', '31', to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), to_char(current_timestamp, 'YYYY-MM-DD HH24:MI:SS'), 'admin', 'admin');

-- 功能评价表
DROP TABLE IF EXISTS eval_checklist;

CREATE TABLE eval_checklist (
    id           VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    function_id  VARCHAR(100) NOT NULL,             -- 功能id
    function_name VARCHAR(255) NOT NULL,            -- 功能名称
    menu_id     VARCHAR(100)  NULL,             -- 菜单id
    create_by    VARCHAR(32) NOT NULL,              -- 创建者
    create_time  VARCHAR(32) NOT NULL,              -- 创建时间
    update_by    VARCHAR(32)  NULL,              -- 更新者
    update_time  VARCHAR(32)  NULL               -- 更新时间
);

-- 注释部分
COMMENT ON COLUMN eval_checklist.id IS '主键';
COMMENT ON COLUMN eval_checklist.function_id IS '功能id';
COMMENT ON COLUMN eval_checklist.function_name IS '功能名称';
COMMENT ON COLUMN eval_checklist.menu_id IS '菜单id';
COMMENT ON COLUMN eval_checklist.create_by IS '创建者';
COMMENT ON COLUMN eval_checklist.create_time IS '创建时间';
COMMENT ON COLUMN eval_checklist.update_by IS '更新者';
COMMENT ON COLUMN eval_checklist.update_time IS '更新时间';



INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('0546e789-03c1-41b6-82cb-ecd9091f7e33'::uuid, 'workbench:evalPoint', '我的工作台', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('ba7063d9-4512-4e23-8c74-50d17545c525'::uuid, 'productManagement:productLibrary:material:evalPoint', '产品管理-产品库-物料', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('cf3f722f-f600-4a7b-b677-dd7422dbfb6a'::uuid, 'productManagement:productLibrary:coreParameters:evalPoint', '产品管理-产品库-核心参数', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b550bd1e-8c45-4e03-942f-432ecb406a7d'::uuid, 'productManagement:productLibrary:brand:evalPoint', '产品管理-产品库-品牌', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('0139e97f-5b99-40d5-ac6c-14ca62b26130'::uuid, 'productManagement:productLibrary:document:evalPoint', '产品管理-产品库-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('a9598957-1578-4fdd-a170-f6bac81e9bdd'::uuid, 'productManagement:productLibrary:faq:evalPoint', '产品管理-产品库-FAQ', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('442b10d2-cbf0-4177-9238-a8fe0b99f3cb'::uuid, 'productManagement:productLibrary:echoWall:evalPoint', '产品管理-产品库-回音墙', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('692b10d2-cbf0-4177-a638-a8fe0b99f3cc'::uuid, 'productManagement:productLibrary:documentAnalyse:evalPoint', 'LTC管理-商机管理-标书分析', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('4769d34b-3a45-4eef-a9c0-a6ef5cbc229c'::uuid, 'productManagement:demandManagement:evalPoint', '产品管理-需求管理', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b88fc6ef-52d0-4459-b7b6-6f4717cbd8c7'::uuid, 'productManagement:selectionManagement:evalPoint', '产品管理-选型管理', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('1181453b-fc26-4d25-909c-f3ba032fe68c'::uuid, 'productManagement:costManagement:evalPoint', '产品管理-成本管理', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('f08f495c-0031-4948-9a1d-3703a4c392fd'::uuid, 'LTCManagement:businessManagement:info:evalPoint', 'LTC管理-商机管理-商机信息', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b6c29ccc-b629-4c90-8626-c078503a9af0'::uuid, 'LTCManagement:businessManagement:selectRequire:evalPoint', 'LTC管理-商机管理-选型需求', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b9593ace-862c-44b9-83b2-f52bbe717c0b'::uuid, 'LTCManagement:businessManagement:clarificationDocument:evalPoint', 'LTC管理-商机管理-标书澄清', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('bcdea7d9-c977-4a3d-802b-82809c6c2547'::uuid, 'LTCManagement:businessManagement:planProduction:evalPoint', 'LTC管理-商机管理-方案制作', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('b3028bb3-176b-4271-ab92-51143e3d5c8a'::uuid, 'LTCManagement:businessManagement:brandGuid:evalPoint', 'LTC管理-商机管理-品牌引导', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('af8fcd53-7afb-4a69-aa47-462c3e1c5a15'::uuid, 'LTCManagement:businessManagement:document:evalPoint', 'LTC管理-商机管理-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('ebbe34cc-ef21-42cb-920d-0d6601426ab0'::uuid, 'LTCManagement:businessManagement:requireBoard:evalPoint', 'LTC管理-商机管理-产品需求看板', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('ec699875-eef2-41fe-bf46-d758c2855be6'::uuid, 'LTCManagement:projectManagement:overviewInfo:evalPoint', 'LTC管理-项目管理-概览信息', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('d8a90824-2731-4750-8bfd-d9f797cfbfa8'::uuid, 'LTCManagement:projectManagement:handoverInfo:evalPoint', 'LTC管理-项目管理-交接信息', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('5e9c6e99-d1f5-4398-b8de-811e2b846e82'::uuid, 'LTCManagement:projectManagement:enginDeep:evalPoint', 'LTC管理-项目管理-工程深化设计', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('bf82e806-889c-4a11-a723-7dff571a4647'::uuid, 'LTCManagement:projectManagement:projectDelivery:evalPoint', 'LTC管理-项目管理-工程交付', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('da52756b-d6d9-4463-a870-5b38bc488d35'::uuid, 'LTCManagement:projectManagement:document:evalPoint', 'LTC管理-项目管理-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('6dd88ee1-f5da-48f7-aaa4-5a526a83f557'::uuid, 'knowledgeBase:document:evalPoint', '知识库-文档', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('f5cc3be9-f127-45da-b7dc-674577a7e507'::uuid, 'knowledgeBase:faq:evalPoint', '知识库-faq', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('033086fe-34fd-4dae-9b89-5e662229f366'::uuid, 'billboard:projectBrand:evalPoint', '看板-项目品牌评分', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('91fcba7d-6122-4b8b-bc6c-93a6bfd968cf'::uuid, 'billboard:productBrand:evalPoint', '看板-产品品牌评分', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);
INSERT INTO public.eval_checklist
(id, function_id, function_name, menu_id, create_by, create_time, update_by, update_time)
VALUES('4d02f0e8-6e68-43b4-b141-31043385a062'::uuid, 'billboard:systemAccess:evalPoint', '看板-系统访问统计', NULL, 'admin', '2025-06-24 10:46:24', NULL, NULL);

-- 用户评价数据表
DROP TABLE IF EXISTS eval_data;

CREATE TABLE eval_data (
    id            VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    function_id   VARCHAR(100) NOT NULL,             -- 功能id
    user_id       VARCHAR(100) NOT NULL,             -- 用户id
    user_name     VARCHAR(255) NOT NULL,             -- 用户名称
    operation_type INTEGER NOT NULL,                 -- 操作类型: 0:赞,1:踩
    eval_content  VARCHAR(1024) NOT NULL,            -- 评价内容
    create_by     VARCHAR(32) NOT NULL,              -- 创建者
    create_time   VARCHAR(32) NOT NULL               -- 创建时间
);
--  创建function_id，user_id，operation_type组合唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_eval_data_function_user_op
ON eval_data(function_id, user_id, operation_type);

-- 注释部分
COMMENT ON COLUMN eval_data.id IS '主键';
COMMENT ON COLUMN eval_data.function_id IS '功能id';
COMMENT ON COLUMN eval_data.user_id IS '用户id';
COMMENT ON COLUMN eval_data.user_name IS '用户名称';
COMMENT ON COLUMN eval_data.operation_type IS '操作类型: 0:赞,1:踩';
COMMENT ON COLUMN eval_data.eval_content IS '评价内容';
COMMENT ON COLUMN eval_data.create_by IS '创建者';
COMMENT ON COLUMN eval_data.create_time IS '创建时间';

-- 用户评价历史数据表
DROP TABLE IF EXISTS eval_data_history;

CREATE TABLE eval_data_history (
    id            VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    function_id   VARCHAR(100) NOT NULL,             -- 功能id
    user_id       VARCHAR(100) NOT NULL,             -- 用户id
    user_name     VARCHAR(255) NOT NULL,             -- 用户名称
    operation_type INTEGER NOT NULL,                 -- 操作类型: 0:赞,1:踩
    eval_content  VARCHAR(500) NOT NULL,             -- 评价内容
    create_by     VARCHAR(32) NOT NULL,              -- 创建者
    create_time   VARCHAR(32) NOT NULL               -- 创建时间
);

-- 注释部分
COMMENT ON COLUMN eval_data_history.id IS '主键';
COMMENT ON COLUMN eval_data_history.function_id IS '功能id';
COMMENT ON COLUMN eval_data_history.user_id IS '用户id';
COMMENT ON COLUMN eval_data_history.user_name IS '用户名称';
COMMENT ON COLUMN eval_data_history.operation_type IS '操作类型: 0:赞,1:踩';
COMMENT ON COLUMN eval_data_history.eval_content IS '评价内容';
COMMENT ON COLUMN eval_data_history.create_by IS '创建者';
COMMENT ON COLUMN eval_data_history.create_time IS '创建时间';


-- 登录统计日表
DROP TABLE IF EXISTS login_stat_day;

CREATE TABLE login_stat_day (
    id         VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day        INTEGER   NOT NULL,             -- 日期（例：20250620）
    dept_id    VARCHAR(100)  NULL,             -- 组织ID
    user_id    VARCHAR(100) NOT NULL,             -- 用户ID
    login_time VARCHAR(32)  NOT NULL              -- 登录时间
);

-- 注释部分
COMMENT ON COLUMN login_stat_day.id IS '主键';
COMMENT ON COLUMN login_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN login_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_day.login_time IS '登录时间';

-- 登录统计周表
DROP TABLE IF EXISTS login_stat_week;

CREATE TABLE login_stat_week (
    id      VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day     INTEGER   NOT NULL,                -- 日期（例：202501）
    dept_id VARCHAR(100)  NULL,             -- 组织ID
    user_id VARCHAR(100) NOT NULL,             -- 用户ID
    num     INTEGER  NOT NULL                  -- 登录天数
);

-- 注释部分
COMMENT ON COLUMN login_stat_week.id IS '主键';
COMMENT ON COLUMN login_stat_week.day IS '日期（例：202501）';
COMMENT ON COLUMN login_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_week.num IS '登录天数';

-- 登录统计月表
DROP TABLE IF EXISTS login_stat_month;

CREATE TABLE login_stat_month (
    id      VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day     INTEGER   NOT NULL,                -- 日期（例：202506）
    dept_id VARCHAR(100)  NULL,             -- 组织ID
    user_id VARCHAR(100) NOT NULL,             -- 用户ID
    num     INTEGER  NOT NULL                  -- 登录天数
);

-- 注释部分
COMMENT ON COLUMN login_stat_month.id IS '主键';
COMMENT ON COLUMN login_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN login_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_month.num IS '登录天数';


-- 登录统计年表
DROP TABLE IF EXISTS login_stat_year;

CREATE TABLE login_stat_year (
    id      VARCHAR(100) NOT NULL PRIMARY KEY, -- 主键
    day     INTEGER   NOT NULL,                -- 日期（例：2025）
    dept_id VARCHAR(100)  NULL,             -- 组织ID
    user_id VARCHAR(100) NOT NULL,             -- 用户ID
    num     INTEGER  NOT NULL                  -- 登录天数
);

-- 注释部分
COMMENT ON COLUMN login_stat_year.id IS '主键';
COMMENT ON COLUMN login_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN login_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN login_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN login_stat_year.num IS '登录天数';

-- 页面访问统计日表
DROP TABLE IF EXISTS page_stat_day;

CREATE TABLE page_stat_day (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：20250620）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL,             -- 访问次数
    create_time         TEXT NOT NULL              -- 记录时间
);

-- 注释部分
COMMENT ON COLUMN page_stat_day.id IS '主表id';
COMMENT ON COLUMN page_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN page_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_day.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_day.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_day.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_day.num IS '访问次数';
COMMENT ON COLUMN page_stat_day.create_time IS '记录时间';

-- 页面访问统计周表
DROP TABLE IF EXISTS page_stat_week;

CREATE TABLE page_stat_week (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：202506）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL              -- 访问次数
);

-- 注释部分
COMMENT ON COLUMN page_stat_week.id IS '主表id';
COMMENT ON COLUMN page_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN page_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_week.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_week.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_week.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_week.num IS '访问次数';


-- 页面访问统计月表
DROP TABLE IF EXISTS page_stat_month;

CREATE TABLE page_stat_month (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：202506）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL              -- 访问次数
);

-- 注释部分
COMMENT ON COLUMN page_stat_month.id IS '主表id';
COMMENT ON COLUMN page_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN page_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_month.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_month.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_month.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_month.num IS '访问次数';

-- 页面访问统计年表
DROP TABLE IF EXISTS page_stat_year;

CREATE TABLE page_stat_year (
    id                  TEXT NOT NULL PRIMARY KEY, -- 主表id
    day                 INTEGER NOT NULL,             -- 日期（例：2025）
    dept_id             TEXT  NULL,             -- 组织ID
    user_id             TEXT NOT NULL,             -- 用户ID
    product_category_id TEXT,                      -- 产品小类id
    type                TEXT,                      -- 1、品牌，2、物料，3文档，4、FAQ
    resource_id          TEXT  NULL,             -- 对应资源ID
    num                 INTEGER NOT NULL              -- 访问次数
);

-- 注释部分
COMMENT ON COLUMN page_stat_year.id IS '主表id';
COMMENT ON COLUMN page_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN page_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN page_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN page_stat_year.product_category_id IS '产品小类id';
COMMENT ON COLUMN page_stat_year.type IS '1、品牌，2、物料，3文档，4、FAQ';
COMMENT ON COLUMN page_stat_year.resource_id IS '对应资源ID';
COMMENT ON COLUMN page_stat_year.num IS '访问次数';

-- 文档下载统计日表
DROP TABLE IF EXISTS file_stat_day;
CREATE TABLE file_stat_day (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE file_stat_day IS '文档下载统计日表';
COMMENT ON COLUMN file_stat_day.id IS '主表id';
COMMENT ON COLUMN file_stat_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN file_stat_day.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_day.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_day.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_day.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_day.preview_num IS '预览次数';
COMMENT ON COLUMN file_stat_day.create_time IS '记录时间';

-- 文档下载统计周表
DROP TABLE IF EXISTS file_stat_week;
CREATE TABLE file_stat_week (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL
);

COMMENT ON TABLE file_stat_week IS '文档下载统计周表';
COMMENT ON COLUMN file_stat_week.id IS '主表id';
COMMENT ON COLUMN file_stat_week.day IS '日期（例：202506）';
COMMENT ON COLUMN file_stat_week.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_week.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_week.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_week.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_week.preview_num IS '预览次数';

-- 文档下载统计月表
DROP TABLE IF EXISTS file_stat_month;
CREATE TABLE file_stat_month (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL
);

COMMENT ON TABLE file_stat_month IS '文档下载统计月表';
COMMENT ON COLUMN file_stat_month.id IS '主表id';
COMMENT ON COLUMN file_stat_month.day IS '日期（例：202506）';
COMMENT ON COLUMN file_stat_month.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_month.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_month.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_month.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_month.preview_num IS '预览次数';

-- 文档下载统计年表
DROP TABLE IF EXISTS file_stat_year;
CREATE TABLE file_stat_year (
    id TEXT NOT NULL PRIMARY KEY,
    day INTEGER NOT NULL,
    dept_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    resource_id TEXT NOT NULL,
    download_num INTEGER NOT NULL,
    preview_num INTEGER NOT NULL
);

COMMENT ON TABLE file_stat_year IS '文档下载统计年表';
COMMENT ON COLUMN file_stat_year.id IS '主表id';
COMMENT ON COLUMN file_stat_year.day IS '日期（例：2025）';
COMMENT ON COLUMN file_stat_year.dept_id IS '组织ID';
COMMENT ON COLUMN file_stat_year.user_id IS '用户ID';
COMMENT ON COLUMN file_stat_year.resource_id IS '资源id';
COMMENT ON COLUMN file_stat_year.download_num IS '下载次数';
COMMENT ON COLUMN file_stat_year.preview_num IS '预览次数';

-- 20250721
DROP TABLE IF EXISTS business_stat_day;
CREATE TABLE public.business_stat_day (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);
DROP TABLE IF EXISTS business_stat_week;
CREATE TABLE public.business_stat_week (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);
DROP TABLE IF EXISTS business_stat_month;
CREATE TABLE public.business_stat_month (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);
DROP TABLE IF EXISTS business_stat_year;
CREATE TABLE public.business_stat_year (
	id text NOT NULL,
	"day" text NULL,
	product_category_id text NULL,
	all_task_num int4 NULL,
	normal_task_num int4 NULL,
	extension_task_num int4 NULL,
	all_lectotype_num int4 NULL,
	bid_lectotype_num int4 NULL,
	appoint_lectotype_num int4 NULL,
	create_time text NULL
);

-- 资产数量统计方案 - 物料相关表
-- 物料资产编辑记录表
CREATE TABLE IF NOT EXISTS asset_material_upd (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    material_id TEXT NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产日表
CREATE TABLE IF NOT EXISTS asset_material_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产周表
CREATE TABLE IF NOT EXISTS asset_material_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产月表
CREATE TABLE IF NOT EXISTS asset_material_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产年表
CREATE TABLE IF NOT EXISTS asset_material_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num int4 NOT NULL,
    up_num int4 NOT NULL,
    up_change_num int4 NOT NULL,
    down_num int4 NOT NULL,
    down_change_num int4 NOT NULL,
    update_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 资产数量统计方案 - 商机相关表
-- 商机资产启动记录表
CREATE TABLE IF NOT EXISTS asset_business_upd (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT,
    project_id TEXT NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产日表
CREATE TABLE IF NOT EXISTS asset_business_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    parent_area_id TEXT,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL,
    CONSTRAINT uk_business_day_area UNIQUE (day, area_id)
);

-- 商机资产周表
CREATE TABLE IF NOT EXISTS asset_business_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    parent_area_id TEXT,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产月表
CREATE TABLE IF NOT EXISTS asset_business_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    parent_area_id TEXT,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 商机资产年表
CREATE TABLE IF NOT EXISTS asset_business_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    area_id TEXT NOT NULL,
    parent_area_id TEXT,
    all_num int4 NOT NULL,
    bid_num int4 NOT NULL,
    sub_bid_num int4 NOT NULL,
    before_bid_num int4 NOT NULL,
    project_approval_num int4 NOT NULL,
    project_add_num int4 NOT NULL,
    project_start_num int4 NOT NULL,
    create_time TEXT NOT NULL
);

-- 物料资产表注释
COMMENT ON TABLE asset_material_upd IS '物料资产编辑记录表';
COMMENT ON COLUMN asset_material_upd.id IS '主表id';
COMMENT ON COLUMN asset_material_upd.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_material_upd.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_upd.material_id IS '物料id';
COMMENT ON COLUMN asset_material_upd.create_time IS '时间';

COMMENT ON TABLE asset_material_day IS '物料资产日表';
COMMENT ON COLUMN asset_material_day.id IS '主表id';
COMMENT ON COLUMN asset_material_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_material_day.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_day.all_num IS '总数';
COMMENT ON COLUMN asset_material_day.up_num IS '已上架物料数（当天上架的物料，头一天没有记录则为0）';
COMMENT ON COLUMN asset_material_day.up_change_num IS '已上架物料变化数（相比头一天的上架数量变化）';
COMMENT ON COLUMN asset_material_day.down_num IS '未上架物料数（当天未上架的物料）';
COMMENT ON COLUMN asset_material_day.down_change_num IS '未上架物料变化数（相比头一天的未上架数量变化）';
COMMENT ON COLUMN asset_material_day.update_num IS '更新物料数（当天编辑的物料）';
COMMENT ON COLUMN asset_material_day.create_time IS '时间';

COMMENT ON TABLE asset_material_week IS '物料资产周表';
COMMENT ON COLUMN asset_material_week.id IS '主表id';
COMMENT ON COLUMN asset_material_week.day IS '日期（例：202501）';
COMMENT ON COLUMN asset_material_week.product_category_id IS '组织ID';
COMMENT ON COLUMN asset_material_week.all_num IS '总数';
COMMENT ON COLUMN asset_material_week.up_num IS '已上架物料数（当周上架的物料头）';
COMMENT ON COLUMN asset_material_week.up_change_num IS '已上架物料变化数（相比头一周的上架数量变化）';
COMMENT ON COLUMN asset_material_week.down_num IS '未上架物料数（当周未上架的物料）';
COMMENT ON COLUMN asset_material_week.down_change_num IS '未上架物料变化数（相比头一周的未上架数量变化）';
COMMENT ON COLUMN asset_material_week.update_num IS '更新物料数（当周编辑的物料）';
COMMENT ON COLUMN asset_material_week.create_time IS '时间';

COMMENT ON TABLE asset_material_month IS '物料资产月表';
COMMENT ON COLUMN asset_material_month.id IS '主表id';
COMMENT ON COLUMN asset_material_month.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_material_month.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_month.all_num IS '总数';
COMMENT ON COLUMN asset_material_month.up_num IS '已上架物料数（当月上架的物料）';
COMMENT ON COLUMN asset_material_month.up_change_num IS '已上架物料变化数（相比头一月的上架数量变化）';
COMMENT ON COLUMN asset_material_month.down_num IS '未上架物料数（当月未上架的物料）';
COMMENT ON COLUMN asset_material_month.down_change_num IS '未上架物料变化数（相比头一月的未上架数量变化）';
COMMENT ON COLUMN asset_material_month.update_num IS '更新物料数（当月编辑的物料）';
COMMENT ON COLUMN asset_material_month.create_time IS '时间';

COMMENT ON TABLE asset_material_year IS '物料资产年表';
COMMENT ON COLUMN asset_material_year.id IS '主表id';
COMMENT ON COLUMN asset_material_year.day IS '日期（例：2025）';
COMMENT ON COLUMN asset_material_year.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_material_year.all_num IS '总数';
COMMENT ON COLUMN asset_material_year.up_num IS '已上架物料数（当年上架的物料）';
COMMENT ON COLUMN asset_material_year.up_change_num IS '已上架物料变化数（相比头一年的上架数量变化）';
COMMENT ON COLUMN asset_material_year.down_num IS '未上架物料数（当年未上架的物料）';
COMMENT ON COLUMN asset_material_year.down_change_num IS '未上架物料变化数（相比头一年的未上架数量变化）';
COMMENT ON COLUMN asset_material_year.update_num IS '更新物料数（当年编辑的物料）';
COMMENT ON COLUMN asset_material_year.create_time IS '时间';

-- 商机资产表注释
COMMENT ON TABLE asset_business_upd IS '商机资产启动记录表';
COMMENT ON COLUMN asset_business_upd.id IS '主表id';
COMMENT ON COLUMN asset_business_upd.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_business_upd.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_upd.project_id IS '商机id';
COMMENT ON COLUMN asset_business_upd.create_time IS '时间';

COMMENT ON TABLE asset_business_day IS '商机资产日表';
COMMENT ON COLUMN asset_business_day.id IS '主表id';
COMMENT ON COLUMN asset_business_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_business_day.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_day.parent_area_id IS '父级地区ID';
COMMENT ON COLUMN asset_business_day.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_day.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_day.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_day.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_day.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_day.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_day.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_day.create_time IS '时间';

COMMENT ON TABLE asset_business_week IS '商机资产周表';
COMMENT ON COLUMN asset_business_week.id IS '主表id';
COMMENT ON COLUMN asset_business_week.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_business_week.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_week.parent_area_id IS '父级地区ID';
COMMENT ON COLUMN asset_business_week.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_week.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_week.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_week.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_week.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_week.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_week.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_week.create_time IS '时间';

COMMENT ON TABLE asset_business_month IS '商机资产月表';
COMMENT ON COLUMN asset_business_month.id IS '主表id';
COMMENT ON COLUMN asset_business_month.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_business_month.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_month.parent_area_id IS '父级地区ID';
COMMENT ON COLUMN asset_business_month.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_month.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_month.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_month.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_month.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_month.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_month.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_month.create_time IS '时间';

COMMENT ON TABLE asset_business_year IS '商机资产年表';
COMMENT ON COLUMN asset_business_year.id IS '主表id';
COMMENT ON COLUMN asset_business_year.day IS '日期（例：2025）';
COMMENT ON COLUMN asset_business_year.area_id IS '地区ID';
COMMENT ON COLUMN asset_business_year.parent_area_id IS '父级地区ID';
COMMENT ON COLUMN asset_business_year.all_num IS '当前的按区域的商机总数';
COMMENT ON COLUMN asset_business_year.bid_num IS '处于投标阶段的商机总数';
COMMENT ON COLUMN asset_business_year.sub_bid_num IS '处于交标阶段的商机总数';
COMMENT ON COLUMN asset_business_year.before_bid_num IS '处于标前阶段的商机总数';
COMMENT ON COLUMN asset_business_year.project_approval_num IS '处于立项阶段的商机总数';
COMMENT ON COLUMN asset_business_year.project_add_num IS '周期内新增的商机总数';
COMMENT ON COLUMN asset_business_year.project_start_num IS '周期内新增的启动了投标的商机总数';
COMMENT ON COLUMN asset_business_year.create_time IS '时间';

-- FAQ资产编辑记录表
CREATE TABLE IF NOT EXISTS asset_faq_upd (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL ,
    product_category_id TEXT NOT NULL,
    faq_id TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_upd IS 'FAQ资产编辑记录表';
COMMENT ON COLUMN asset_faq_upd.id IS '主表id';
COMMENT ON COLUMN asset_faq_upd.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_faq_upd.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_upd.faq_id IS 'faq的id';
COMMENT ON COLUMN asset_faq_upd.create_time IS '时间';

-- FAQ资产日表
CREATE TABLE IF NOT EXISTS asset_faq_day (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_day IS 'FAQ资产日表';
COMMENT ON COLUMN asset_faq_day.id IS '主表id';
COMMENT ON COLUMN asset_faq_day.day IS '日期（例：20250620）';
COMMENT ON COLUMN asset_faq_day.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_day.all_num IS '总FAQ数（所有的FAQ）';
COMMENT ON COLUMN asset_faq_day.change_num IS 'FAQ变化数（相比头一天的数量变化）';
COMMENT ON COLUMN asset_faq_day.update_num IS '更新FAQ数（当天编辑的FAQ）';
COMMENT ON COLUMN asset_faq_day.create_time IS '时间';

-- FAQ资产周表
CREATE TABLE IF NOT EXISTS asset_faq_week (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_week IS 'FAQ资产周表';
COMMENT ON COLUMN asset_faq_week.id IS '主表id';
COMMENT ON COLUMN asset_faq_week.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_faq_week.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_week.all_num IS '总FAQ数';
COMMENT ON COLUMN asset_faq_week.change_num IS 'FAQ变化数';
COMMENT ON COLUMN asset_faq_week.update_num IS '更新FAQ数';
COMMENT ON COLUMN asset_faq_week.create_time IS '时间';

-- FAQ资产月表：
CREATE TABLE IF NOT EXISTS asset_faq_month (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_month IS 'FAQ资产月表';
COMMENT ON COLUMN asset_faq_month.id IS '主表id';
COMMENT ON COLUMN asset_faq_month.day IS '日期（例：202506）';
COMMENT ON COLUMN asset_faq_month.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_month.all_num IS '总FAQ数';
COMMENT ON COLUMN asset_faq_month.change_num IS 'FAQ变化数';
COMMENT ON COLUMN asset_faq_month.update_num IS '更新FAQ数';
COMMENT ON COLUMN asset_faq_month.create_time IS '时间';

-- FAQ资产年表：
CREATE TABLE IF NOT EXISTS asset_faq_year (
    id TEXT NOT NULL PRIMARY KEY,
    day TEXT NOT NULL,
    product_category_id TEXT NOT NULL,
    all_num TEXT NOT NULL,
    change_num TEXT NOT NULL,
    update_num TEXT NOT NULL,
    create_time TEXT NOT NULL
);

COMMENT ON TABLE asset_faq_year IS 'FAQ资产年表';
COMMENT ON COLUMN asset_faq_year.id IS '主表id';
COMMENT ON COLUMN asset_faq_year.day IS '日期（例：2025）';
COMMENT ON COLUMN asset_faq_year.product_category_id IS '小类ID';
COMMENT ON COLUMN asset_faq_year.all_num IS '总FAQ数（所有的FAQ）';
COMMENT ON COLUMN asset_faq_year.change_num IS 'FAQ变化数（相比头一天的数量变化）';
COMMENT ON COLUMN asset_faq_year.update_num IS '更新FAQ数（当天编辑的FAQ）';
COMMENT ON COLUMN asset_faq_year.create_time IS '时间';