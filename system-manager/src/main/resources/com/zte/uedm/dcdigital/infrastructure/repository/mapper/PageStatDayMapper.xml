<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:6d6954f27f8e47a147c10976d02f1c782679a667 -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.PageStatDayMapper">

    <resultMap id="PageStatDayResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatDayPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="productCategoryId" column="product_category_id"/>
        <result property="type" column="type"/>
        <result property="resourceId" column="resource_id"/>
        <result property="num" column="num"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <resultMap id="PageStatDtoResult" type="com.zte.uedm.dcdigital.interfaces.web.dto.PageVisitDto">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="productCategoryId" column="product_category_id"/>
        <result property="type" column="type"/>
        <result property="resourceId" column="resource_id"/>
        <result property="num" column="num"/>
        <result property="weekId" column="week_id"/>
        <result property="weekNum" column="week_num"/>
        <result property="monthId" column="month_id"/>
        <result property="monthNum" column="month_num"/>
        <result property="yearId" column="year_id"/>
        <result property="yearNum" column="year_num"/>
    </resultMap>

    <resultMap id="PageStatAllTypeResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="productCategoryId" column="product_category_id"/>
        <result property="type" column="type"/>
        <result property="resourceId" column="resource_id"/>
        <result property="num" column="num"/>
    </resultMap>

    <sql id="selectPageStatDayVo">
        SELECT id, day, dept_id, user_id, product_category_id, type, resource_id, num, create_time
        FROM page_stat_day
    </sql>

    <insert id="insertPageStatDay" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatDayPo">
        INSERT INTO page_stat_day (id, day, dept_id, user_id, product_category_id, type, resource_id, num, create_time)
        VALUES (#{id}, #{day}, #{deptId}, #{userId}, #{productCategoryId}, #{type}, #{resourceId}, #{num}, #{createTime})
    </insert>

    <insert id="batchInsertPageStatDay">
        INSERT INTO page_stat_day (id, day, dept_id, user_id, product_category_id, type, resource_id, num, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.day}, #{item.deptId}, #{item.userId}, #{item.productCategoryId}, #{item.type}, #{item.resourceId}, #{item.num}, #{item.createTime})
        </foreach>
    </insert>

    <update id="batchUpdatePageStatDay">
        UPDATE page_stat_day
        SET
        num = CASE id
        <foreach collection="list" item="item" separator=" ">
            WHEN #{item.id} THEN #{item.num}
        </foreach>
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <update id="updatePageStatDay">
        UPDATE page_stat_day
        SET num = #{num}
        WHERE id = #{id}
    </update>

    <select id="selectPageStatDayList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatDayPo"
            resultMap="PageStatDayResult">
        <include refid="selectPageStatDayVo"/>
        <where>
            <if test="day != null">AND day = #{day}</if>
            <if test="yearTime != null">
                AND (day / 10000) = #{yearTime}
            </if>
            <if test="deptId != null and deptId != ''">AND dept_id = #{deptId}</if>
            <if test="userId != null and userId != ''">AND user_id = #{userId}</if>
            <if test="beginTime != null and endTime != null">
                AND day BETWEEN #{beginTime} AND #{endTime}
            </if>
            <if test="type != null and type != ''">AND type = #{type}</if>
            <if test="resourceId != null and resourceId != ''">AND resource_id = #{resourceId}</if>
            <if test="productCategoryId != null and productCategoryId != ''">AND product_category_id = #{productCategoryId}</if>
        </where>
    </select>

    <delete id="deletePageStatDayByIds" parameterType="String">
        DELETE FROM page_stat_day
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectPageStatDayById"
            parameterType="String"
            resultMap="PageStatDayResult">
        <include refid="selectPageStatDayVo"/>
        WHERE id = #{id}
    </select>
    <select id="selectPageStatDayDataByCondition" resultMap="PageStatDayResult">
        <include refid="selectPageStatDayVo"/>
        <where>
            <if test="day != null">AND day = #{day}</if>
            <if test="deptId != null">AND dept_id = #{deptId}</if>
            <if test="userId != null and userId != ''">AND user_id = #{userId}</if>
            <if test="type != null and type != ''">AND type = #{type}</if>
            <if test="resourceId != null and resourceId != ''">AND resource_id = #{resourceId}</if>
            <if test="productCategoryId != null and productCategoryId != ''">AND product_category_id = #{productCategoryId}</if>
        </where>
    </select>
    <select id="getPageVisitStatWithDataList" resultMap="PageStatDtoResult">
        select
        d.id,
        d.user_id,
        d.dept_id,
        d.product_category_id,
        d.type,
        d.resource_id,
        d.num,
        w.id as week_id,
        w.num as week_num,

        m.id as month_id,
        m.num as month_num,

        y.id as year_id,
        y.num as year_num
        from
        page_stat_day d
        left join page_stat_week w
        on
        d.user_id = w.user_id
        and d.dept_id = w.dept_id
        and w.day = #{previousWeekNumber}
        left join page_stat_month m

        on
        d.user_id = m.user_id
        and d.dept_id = m.dept_id
        and m.day = #{previousMonthNumber}
        left join page_stat_year y
        on
        d.user_id = y.user_id
        and d.dept_id = y.dept_id
        and y.day = #{previousYearNumber}
        where
        d.day = #{previousDayNumber};
    </select>
</mapper>

        <!-- Ended by AICoder, pid:6d6954f27f8e47a147c10976d02f1c782679a667 -->