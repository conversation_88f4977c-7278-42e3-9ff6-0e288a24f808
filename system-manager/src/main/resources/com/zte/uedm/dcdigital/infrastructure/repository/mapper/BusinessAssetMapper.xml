<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessAssetMapper">

    <select id="selectDayDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_day
        WHERE day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <select id="selectWeekDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetWeekEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_week
        WHERE day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <select id="selectMonthDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetMonthEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_month
        WHERE day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <select id="selectYearDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetYearEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_year
        WHERE day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <!-- 根据地区ID和时间范围查询统计记录 -->
    <select id="selectDayDateByAreaIdAndTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_day
        WHERE area_id = #{areaId}
          AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <select id="selectWeekDateByAreaIdAndTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetWeekEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_week
        WHERE area_id = #{areaId}
          AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <select id="selectMonthDateByAreaIdAndTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetMonthEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_month
        WHERE area_id = #{areaId}
          AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <select id="selectYearDateByAreaIdAndTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetYearEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_year
        WHERE area_id = #{areaId}
          AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day DESC
    </select>

    <!-- 查询指定地区的下级地区商机统计数据 -->
    <select id="selectJuniorAreaBusinessData" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity">
        <choose>
            <when test="timeType == 1">
                <!-- 按天查询下级地区数据 -->
                SELECT
                    abd.id,
                    abd.day,
                    abd.area_id,
                    abd.all_num,
                    abd.bid_num,
                    abd.sub_bid_num,
                    abd.before_bid_num,
                    abd.project_approval_num,
                    abd.project_add_num,
                    abd.project_start_num,
                    abd.create_time
                FROM asset_business_day abd
                WHERE abd.area_id IN (
                    SELECT area_id
                    FROM sys_area
                    WHERE parent_id = #{areaId}
                )
                AND abd.day BETWEEN #{startTime} AND #{endTime}
                ORDER BY abd.area_id, abd.day DESC
            </when>
            <when test="timeType == 2">
                <!-- 按周查询下级地区数据 -->
                SELECT
                    abw.id,
                    abw.day,
                    abw.area_id,
                    abw.all_num,
                    abw.bid_num,
                    abw.sub_bid_num,
                    abw.before_bid_num,
                    abw.project_approval_num,
                    abw.project_add_num,
                    abw.project_start_num,
                    abw.create_time
                FROM asset_business_week abw
                WHERE abw.area_id IN (
                    SELECT area_id
                    FROM sys_area
                    WHERE parent_id = #{areaId}
                )
                AND abw.day BETWEEN #{startTime} AND #{endTime}
                ORDER BY abw.area_id, abw.day DESC
            </when>
            <when test="timeType == 3">
                <!-- 按月查询下级地区数据 -->
                SELECT
                    abm.id,
                    abm.day,
                    abm.area_id,
                    abm.all_num,
                    abm.bid_num,
                    abm.sub_bid_num,
                    abm.before_bid_num,
                    abm.project_approval_num,
                    abm.project_add_num,
                    abm.project_start_num,
                    abm.create_time
                FROM asset_business_month abm
                WHERE abm.area_id IN (
                    SELECT area_id
                    FROM sys_area
                    WHERE parent_id = #{areaId}
                )
                AND abm.day BETWEEN #{startTime} AND #{endTime}
                ORDER BY abm.area_id, abm.day DESC
            </when>
            <when test="timeType == 4">
                <!-- 按年查询下级地区数据 -->
                SELECT
                    aby.id,
                    aby.day,
                    aby.area_id,
                    aby.all_num,
                    aby.bid_num,
                    aby.sub_bid_num,
                    aby.before_bid_num,
                    aby.project_approval_num,
                    aby.project_add_num,
                    aby.project_start_num,
                    aby.create_time
                FROM asset_business_year aby
                WHERE aby.area_id IN (
                    SELECT area_id
                    FROM sys_area
                    WHERE parent_id = #{areaId}
                )
                AND aby.day BETWEEN #{startTime} AND #{endTime}
                ORDER BY aby.area_id, aby.day DESC
            </when>
        </choose>
    </select>

    <!-- 插入商机启动投标记录 -->
    <insert id="insertBusinessStartRecord">
        INSERT INTO asset_business_upd (
            id,
            day,
            area_id,
            project_id,
            create_time
        ) VALUES (
            #{projectId}_#{day}_#{areaId},
            #{day},
            #{areaId},
            #{projectId},
            CURRENT_TIMESTAMP
        )
    </insert>

    <!-- 插入商机启动投标记录（使用实体对象） -->
    <insert id="insertBusinessStartRecordEntity" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetUpdEntity">
        INSERT INTO asset_business_upd (
            id,
            day,
            area_id,
            project_id,
            create_time
        ) VALUES (
            #{id},
            #{day},
            #{areaId},
            #{projectId},
            #{createTime}
        )
    </insert>

    <!-- 批量插入日表数据 -->
    <insert id="batchInsertDayData" parameterType="java.util.List">
        INSERT INTO asset_business_day (
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.day},
                #{item.areaId},
                #{item.parentAreaId},
                #{item.allNum},
                #{item.bidNum},
                #{item.subBidNum},
                #{item.beforeBidNum},
                #{item.projectApprovalNum},
                #{item.projectAddNum},
                #{item.projectStartNum},
                #{item.createTime}
            )
        </foreach>
        ON CONFLICT (day, area_id) DO UPDATE SET
            id = EXCLUDED.id,
            parent_area_id = EXCLUDED.parent_area_id,
            all_num = EXCLUDED.all_num,
            bid_num = EXCLUDED.bid_num,
            sub_bid_num = EXCLUDED.sub_bid_num,
            before_bid_num = EXCLUDED.before_bid_num,
            project_approval_num = EXCLUDED.project_approval_num,
            project_add_num = EXCLUDED.project_add_num,
            project_start_num = EXCLUDED.project_start_num,
            create_time = EXCLUDED.create_time
    </insert>

    <!-- 批量插入周表数据 -->
    <insert id="batchInsertWeekData" parameterType="java.util.List">
        INSERT INTO asset_business_week (
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.day},
                #{item.areaId},
                #{item.parentAreaId},
                #{item.allNum},
                #{item.bidNum},
                #{item.subBidNum},
                #{item.beforeBidNum},
                #{item.projectApprovalNum},
                #{item.projectAddNum},
                #{item.projectStartNum},
                #{item.createTime}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
            all_num = EXCLUDED.all_num,
            bid_num = EXCLUDED.bid_num,
            sub_bid_num = EXCLUDED.sub_bid_num,
            before_bid_num = EXCLUDED.before_bid_num,
            project_approval_num = EXCLUDED.project_approval_num,
            project_add_num = EXCLUDED.project_add_num,
            project_start_num = EXCLUDED.project_start_num,
            parent_area_id = EXCLUDED.parent_area_id,
            create_time = EXCLUDED.create_time
    </insert>

    <!-- 批量插入月表数据 -->
    <insert id="batchInsertMonthData" parameterType="java.util.List">
        INSERT INTO asset_business_month (
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.day},
                #{item.areaId},
                #{item.parentAreaId},
                #{item.allNum},
                #{item.bidNum},
                #{item.subBidNum},
                #{item.beforeBidNum},
                #{item.projectApprovalNum},
                #{item.projectAddNum},
                #{item.projectStartNum},
                #{item.createTime}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
            all_num = EXCLUDED.all_num,
            bid_num = EXCLUDED.bid_num,
            sub_bid_num = EXCLUDED.sub_bid_num,
            before_bid_num = EXCLUDED.before_bid_num,
            project_approval_num = EXCLUDED.project_approval_num,
            project_add_num = EXCLUDED.project_add_num,
            project_start_num = EXCLUDED.project_start_num,
            parent_area_id = EXCLUDED.parent_area_id,
            create_time = EXCLUDED.create_time
    </insert>

    <!-- 批量插入年表数据 -->
    <insert id="batchInsertYearData" parameterType="java.util.List">
        INSERT INTO asset_business_year (
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id},
                #{item.day},
                #{item.areaId},
                #{item.parentAreaId},
                #{item.allNum},
                #{item.bidNum},
                #{item.subBidNum},
                #{item.beforeBidNum},
                #{item.projectApprovalNum},
                #{item.projectAddNum},
                #{item.projectStartNum},
                #{item.createTime}
            )
        </foreach>
        ON CONFLICT (id) DO UPDATE SET
            all_num = EXCLUDED.all_num,
            bid_num = EXCLUDED.bid_num,
            sub_bid_num = EXCLUDED.sub_bid_num,
            before_bid_num = EXCLUDED.before_bid_num,
            project_approval_num = EXCLUDED.project_approval_num,
            project_add_num = EXCLUDED.project_add_num,
            project_start_num = EXCLUDED.project_start_num,
            parent_area_id = EXCLUDED.parent_area_id,
            create_time = EXCLUDED.create_time
    </insert>

    <!-- 根据日期查询启动投标记录数量 -->
    <select id="countBusinessStartRecordsByDay" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM asset_business_upd
        WHERE day = #{day}
        <if test="areaId != null and areaId != ''">
            AND area_id = #{areaId}
        </if>
    </select>

    <!-- 根据时间范围查询启动投标记录数量 -->
    <select id="countBusinessStartRecordsByTimeRange" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM asset_business_upd
        WHERE day BETWEEN #{startTime} AND #{endTime}
        <if test="areaId != null and areaId != ''">
            AND area_id = #{areaId}
        </if>
    </select>

    <!-- 检查日表数据是否存在 -->
    <select id="checkDayDataExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM asset_business_day
        WHERE day = #{day} AND area_id = #{areaId}
    </select>

    <!-- 检查周表数据是否存在 -->
    <select id="checkWeekDataExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM asset_business_week
        WHERE day = #{day} AND area_id = #{areaId}
    </select>

    <!-- 检查月表数据是否存在 -->
    <select id="checkMonthDataExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM asset_business_month
        WHERE day = #{day} AND area_id = #{areaId}
    </select>

    <!-- 检查年表数据是否存在 -->
    <select id="checkYearDataExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM asset_business_year
        WHERE day = #{day} AND area_id = #{areaId}
    </select>

    <!-- 更新日表数据 -->
    <update id="updateDayData">
        UPDATE asset_business_day
        SET
            all_num = #{allNum},
            bid_num = #{bidNum},
            sub_bid_num = #{subBidNum},
            before_bid_num = #{beforeBidNum},
            project_approval_num = #{projectApprovalNum},
            project_add_num = #{projectAddNum},
            project_start_num = #{projectStartNum},
            create_time = #{createTime}
        WHERE day = #{day} AND area_id = #{areaId}
    </update>

    <!-- 更新周表数据 -->
    <update id="updateWeekData">
        UPDATE asset_business_week
        SET
            all_num = #{allNum},
            bid_num = #{bidNum},
            sub_bid_num = #{subBidNum},
            before_bid_num = #{beforeBidNum},
            project_approval_num = #{projectApprovalNum},
            project_add_num = #{projectAddNum},
            project_start_num = #{projectStartNum},
            create_time = #{createTime}
        WHERE day = #{day} AND area_id = #{areaId}
    </update>

    <!-- 更新月表数据 -->
    <update id="updateMonthData">
        UPDATE asset_business_month
        SET
            all_num = #{allNum},
            bid_num = #{bidNum},
            sub_bid_num = #{subBidNum},
            before_bid_num = #{beforeBidNum},
            project_approval_num = #{projectApprovalNum},
            project_add_num = #{projectAddNum},
            project_start_num = #{projectStartNum},
            create_time = #{createTime}
        WHERE day = #{day} AND area_id = #{areaId}
    </update>

    <!-- 更新年表数据 -->
    <update id="updateYearData">
        UPDATE asset_business_year
        SET
            all_num = #{allNum},
            bid_num = #{bidNum},
            sub_bid_num = #{subBidNum},
            before_bid_num = #{beforeBidNum},
            project_approval_num = #{projectApprovalNum},
            project_add_num = #{projectAddNum},
            project_start_num = #{projectStartNum},
            create_time = #{createTime}
        WHERE day = #{day} AND area_id = #{areaId}
    </update>

    <!-- 根据地区ID查询所有下级地区ID -->
    <select id="selectSubAreaIds" resultType="java.lang.String">
        WITH RECURSIVE area_tree AS (
            -- 查询直接下级
            SELECT area_id, area_name, parent_id, 1 as level
            FROM sys_area
            WHERE parent_id = #{areaId}

            UNION ALL

            -- 递归查询下级的下级
            SELECT sa.area_id, sa.area_name, sa.parent_id, at.level + 1
            FROM sys_area sa
            INNER JOIN area_tree at ON sa.parent_id = at.area_id
            WHERE at.level &lt; 10  -- 防止无限递归，最多10层
        )
        SELECT area_id FROM area_tree
        ORDER BY level, area_id
    </select>

    <!-- 查询地区名称 -->
    <select id="selectAreaNameById" resultType="java.lang.String">
        SELECT area_name
        FROM sys_area
        WHERE area_id = #{areaId}
    </select>

    <!-- 统计天表中的总记录数 -->
    <select id="countAllDayData" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM asset_business_day
    </select>

    <!-- 查询天表中所有不重复的日期 -->
    <select id="selectAllDistinctDays" resultType="java.lang.String">
        SELECT DISTINCT day
        FROM asset_business_day
        ORDER BY day ASC
    </select>

    <!-- 根据父节点ID和时间点查询所有子节点的日表数据 -->
    <select id="selectChildAreaDayDataByTimePoint" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_day
        WHERE parent_area_id = #{parentAreaId}
          AND day = #{timePoint}
        ORDER BY area_id
    </select>

    <!-- 根据父节点ID和时间点查询所有子节点的周表数据 -->
    <select id="selectChildAreaWeekDataByTimePoint" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetWeekEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_week
        WHERE parent_area_id = #{parentAreaId}
          AND day = #{timePoint}
        ORDER BY area_id
    </select>

    <!-- 根据父节点ID和时间点查询所有子节点的月表数据 -->
    <select id="selectChildAreaMonthDataByTimePoint" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetMonthEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_month
        WHERE parent_area_id = #{parentAreaId}
          AND day = #{timePoint}
        ORDER BY area_id
    </select>

    <!-- 根据父节点ID和时间点查询所有子节点的年表数据 -->
    <select id="selectChildAreaYearDataByTimePoint" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetYearEntity">
        SELECT
            id,
            day,
            area_id,
            parent_area_id,
            all_num,
            bid_num,
            sub_bid_num,
            before_bid_num,
            project_approval_num,
            project_add_num,
            project_start_num,
            create_time
        FROM asset_business_year
        WHERE parent_area_id = #{parentAreaId}
          AND day = #{timePoint}
        ORDER BY area_id
    </select>

    <!-- 根据父节点ID和时间范围查询所有子节点的日表数据（用于统计列表） -->
    <select id="selectChildAreaDayDataByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetDayEntity">
        <!-- 首先尝试查询以传入ID为父ID的子节点数据 -->
        WITH child_data AS (
            SELECT
                day,
                SUM(all_num) AS all_num,
                SUM(bid_num) AS bid_num,
                SUM(sub_bid_num) AS sub_bid_num,
                SUM(before_bid_num) AS before_bid_num,
                SUM(project_approval_num) AS project_approval_num,
                SUM(project_add_num) AS project_add_num,
                SUM(project_start_num) AS project_start_num
            FROM asset_business_day
            WHERE parent_area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
            GROUP BY day
        ),
        self_data AS (
            SELECT
                day,
                all_num,
                bid_num,
                sub_bid_num,
                before_bid_num,
                project_approval_num,
                project_add_num,
                project_start_num
            FROM asset_business_day
            WHERE area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
        )
        SELECT * FROM child_data
        UNION ALL
        SELECT * FROM self_data
        WHERE NOT EXISTS (SELECT 1 FROM child_data)
        ORDER BY day DESC
    </select>

    <!-- 根据父节点ID和时间范围查询所有子节点的周表数据（用于统计列表） -->
    <select id="selectChildAreaWeekDataByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetWeekEntity">
        <!-- 首先尝试查询以传入ID为父ID的子节点数据 -->
        WITH child_data AS (
            SELECT
                day,
                SUM(all_num) AS all_num,
                SUM(bid_num) AS bid_num,
                SUM(sub_bid_num) AS sub_bid_num,
                SUM(before_bid_num) AS before_bid_num,
                SUM(project_approval_num) AS project_approval_num,
                SUM(project_add_num) AS project_add_num,
                SUM(project_start_num) AS project_start_num
            FROM asset_business_week
            WHERE parent_area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
            GROUP BY day
        ),
        self_data AS (
            SELECT
                day,
                all_num,
                bid_num,
                sub_bid_num,
                before_bid_num,
                project_approval_num,
                project_add_num,
                project_start_num
            FROM asset_business_week
            WHERE area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
        )
        SELECT * FROM child_data
        UNION ALL
        SELECT * FROM self_data
        WHERE NOT EXISTS (SELECT 1 FROM child_data)
        ORDER BY day DESC
    </select>

    <!-- 根据父节点ID和时间范围查询所有子节点的月表数据（用于统计列表） -->
    <select id="selectChildAreaMonthDataByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetMonthEntity">
        <!-- 首先尝试查询以传入ID为父ID的子节点数据 -->
        WITH child_data AS (
            SELECT
                day,
                SUM(all_num) AS all_num,
                SUM(bid_num) AS bid_num,
                SUM(sub_bid_num) AS sub_bid_num,
                SUM(before_bid_num) AS before_bid_num,
                SUM(project_approval_num) AS project_approval_num,
                SUM(project_add_num) AS project_add_num,
                SUM(project_start_num) AS project_start_num
            FROM asset_business_month
            WHERE parent_area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
            GROUP BY day
        ),
        self_data AS (
            SELECT
                day,
                all_num,
                bid_num,
                sub_bid_num,
                before_bid_num,
                project_approval_num,
                project_add_num,
                project_start_num
            FROM asset_business_month
            WHERE area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
        )
        SELECT * FROM child_data
        UNION ALL
        SELECT * FROM self_data
        WHERE NOT EXISTS (SELECT 1 FROM child_data)
        ORDER BY day DESC
    </select>

    <!-- 根据父节点ID和时间范围查询所有子节点的年表数据（用于统计列表） -->
    <select id="selectChildAreaYearDataByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.BusinessAssetYearEntity">
        <!-- 首先尝试查询以传入ID为父ID的子节点数据 -->
        WITH child_data AS (
            SELECT
                day,
                SUM(all_num) AS all_num,
                SUM(bid_num) AS bid_num,
                SUM(sub_bid_num) AS sub_bid_num,
                SUM(before_bid_num) AS before_bid_num,
                SUM(project_approval_num) AS project_approval_num,
                SUM(project_add_num) AS project_add_num,
                SUM(project_start_num) AS project_start_num
            FROM asset_business_year
            WHERE parent_area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
            GROUP BY day
        ),
        self_data AS (
            SELECT
                day,
                all_num,
                bid_num,
                sub_bid_num,
                before_bid_num,
                project_approval_num,
                project_add_num,
                project_start_num
            FROM asset_business_year
            WHERE area_id = #{parentAreaId}
              AND day BETWEEN #{startTime} AND #{endTime}
        )
        SELECT * FROM child_data
        UNION ALL
        SELECT * FROM self_data
        WHERE NOT EXISTS (SELECT 1 FROM child_data)
        ORDER BY day DESC
    </select>

</mapper>
