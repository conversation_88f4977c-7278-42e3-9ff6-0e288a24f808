<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:ge3730ee469183b140d10b7a20a20f46add8bd33 -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.LoginStatDayMapper">

    <resultMap id="LoginStatDayResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatDayPo">
        <id property="id" column="id"/>
        <result property="day" column="day"/>
        <result property="deptId" column="dept_id"/>
        <result property="userId" column="user_id"/>
        <result property="loginTime" column="login_time"/>
    </resultMap>

    <resultMap id="LoginStatDtoResult" type="com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatDto">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>

        <result property="weekId" column="week_id"/>
        <result property="weekNum" column="week_num"/>
        <result property="monthId" column="month_id"/>
        <result property="monthNum" column="month_num"/>
        <result property="yearId" column="year_id"/>
        <result property="yearNum" column="year_num"/>
    </resultMap>

    <sql id="selectLoginStatDayVo">
        SELECT id, day, dept_id, user_id, login_time
        FROM login_stat_day
    </sql>

    <insert id="insertLoginStatDay" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatDayPo">
        INSERT INTO login_stat_day (id, day, dept_id, user_id, login_time)
        VALUES (#{id}, #{day}, #{deptId}, #{userId}, #{loginTime})
    </insert>

    <select id="selectLoginStatDayList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatDayPo"
            resultMap="LoginStatDayResult">
        <include refid="selectLoginStatDayVo"/>
        <where>
            <if test="day != null">AND day = #{day}</if>
            <if test="yearTime != null">
                AND (day / 10000) = #{yearTime}
            </if>
            <if test="deptId != null">AND dept_id = #{deptId}</if>
            <if test="userId != null and userId != ''">AND user_id = #{userId}</if>
            <if test="beginTime != null and endTime != null">
                AND day BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
    </select>

    <delete id="deleteLoginStatDayByIds" parameterType="String">
        DELETE FROM login_stat_day
        WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectLoginStatDayById"
            parameterType="String"
            resultMap="LoginStatDayResult">
        <include refid="selectLoginStatDayVo"/>
        WHERE id = #{id}
    </select>
    <select id="getLoginStatWithDataList" resultMap="LoginStatDtoResult">
        select
        d.id,
        d.user_id,
        d.dept_id,

        w.id as week_id,
        w.num as week_num,

        m.id as month_id,
        m.num as month_num,

        y.id as year_id,
        y.num as year_num
        from
        login_stat_day d
        left join login_stat_week w
        on
        d.user_id = w.user_id
        and d.dept_id = w.dept_id
        and w.day = #{previousWeekNumber}
        left join login_stat_month m

        on
        d.user_id = m.user_id
        and d.dept_id = m.dept_id
        and m.day = #{previousMonthNumber}
        left join login_stat_year y
        on
        d.user_id = y.user_id
        and d.dept_id = y.dept_id
        and y.day = #{previousYearNumber}
        where
        d.day = #{previousDayNumber};
    </select>
</mapper>

        <!-- Ended by AICoder, pid:ge3730ee469183b140d10b7a20a20f46add8bd33 -->