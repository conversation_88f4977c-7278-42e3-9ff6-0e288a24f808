<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.MaterialAssetMapper">

    <!-- 日表结果映射 -->
    <resultMap id="MaterialAssetDayResultMap" type="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetDayEntity">
        <id column="id" property="id"/>
        <result column="day" property="day"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="all_num" property="allNum"/>
        <result column="up_num" property="upNum"/>
        <result column="up_change_num" property="upChangeNum"/>
        <result column="down_num" property="downNum"/>
        <result column="down_change_num" property="downChangeNum"/>
        <result column="update_num" property="updateNum"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 周表结果映射 -->
    <resultMap id="MaterialAssetWeekResultMap" type="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetWeekEntity">
        <id column="id" property="id"/>
        <result column="day" property="day"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="all_num" property="allNum"/>
        <result column="up_num" property="upNum"/>
        <result column="up_change_num" property="upChangeNum"/>
        <result column="down_num" property="downNum"/>
        <result column="down_change_num" property="downChangeNum"/>
        <result column="update_num" property="updateNum"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 月表结果映射 -->
    <resultMap id="MaterialAssetMonthResultMap" type="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetMonthEntity">
        <id column="id" property="id"/>
        <result column="day" property="day"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="all_num" property="allNum"/>
        <result column="up_num" property="upNum"/>
        <result column="up_change_num" property="upChangeNum"/>
        <result column="down_num" property="downNum"/>
        <result column="down_change_num" property="downChangeNum"/>
        <result column="update_num" property="updateNum"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 年表结果映射 -->
    <resultMap id="MaterialAssetYearResultMap" type="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetYearEntity">
        <id column="id" property="id"/>
        <result column="day" property="day"/>
        <result column="product_category_id" property="productCategoryId"/>
        <result column="all_num" property="allNum"/>
        <result column="up_num" property="upNum"/>
        <result column="up_change_num" property="upChangeNum"/>
        <result column="down_num" property="downNum"/>
        <result column="down_change_num" property="downChangeNum"/>
        <result column="update_num" property="updateNum"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 查询日表数据 -->
    <select id="queryDayData" resultMap="MaterialAssetDayResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_day
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day BETWEEN #{startDay} AND #{endDay}
        ORDER BY day ASC, product_category_id ASC
    </select>

    <!-- 查询周表数据 -->
    <select id="queryWeekData" resultMap="MaterialAssetWeekResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_week
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day ASC, product_category_id ASC
    </select>

    <!-- 查询月表数据 -->
    <select id="queryMonthData" resultMap="MaterialAssetMonthResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_month
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day ASC, product_category_id ASC
    </select>

    <!-- 查询年表数据 -->
    <select id="queryYearData" resultMap="MaterialAssetYearResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_year
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day BETWEEN #{startTime} AND #{endTime}
        ORDER BY day ASC, product_category_id ASC
    </select>

    <!-- 查询单个时间点的日表数据 -->
    <select id="queryDayDataByTimePoint" resultMap="MaterialAssetDayResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_day
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day = #{timePoint}
        ORDER BY product_category_id ASC
    </select>

    <!-- 查询单个时间点的周表数据 -->
    <select id="queryWeekDataByTimePoint" resultMap="MaterialAssetWeekResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_week
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day = #{timePoint}
        ORDER BY product_category_id ASC
    </select>

    <!-- 查询单个时间点的月表数据 -->
    <select id="queryMonthDataByTimePoint" resultMap="MaterialAssetMonthResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_month
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day = #{timePoint}
        ORDER BY product_category_id ASC
    </select>

    <!-- 查询单个时间点的年表数据 -->
    <select id="queryYearDataByTimePoint" resultMap="MaterialAssetYearResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_year
        WHERE product_category_id IN
        <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
        AND day = #{timePoint}
        ORDER BY product_category_id ASC
    </select>

    <!-- 查询前一个时间点的日表数据 -->
    <select id="queryPreviousDayData" resultMap="MaterialAssetDayResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_day
        WHERE product_category_id = #{productCategoryId}
        AND day = #{timePoint}
        ORDER BY day DESC
        LIMIT 1
    </select>

    <!-- 查询前一个时间点的周表数据 -->
    <select id="queryPreviousWeekData" resultMap="MaterialAssetWeekResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_week
        WHERE product_category_id = #{productCategoryId}
        AND day = #{timePoint}
        ORDER BY day DESC
        LIMIT 1
    </select>

    <!-- 查询前一个时间点的月表数据 -->
    <select id="queryPreviousMonthData" resultMap="MaterialAssetMonthResultMap">
        SELECT 
            id, day, product_category_id, all_num, up_num, up_change_num, 
            down_num, down_change_num, update_num, create_time
        FROM asset_material_month
        WHERE product_category_id = #{productCategoryId}
        AND day = #{timePoint}
        ORDER BY day DESC
        LIMIT 1
    </select>

    <!-- 查询前一个时间点的年表数据 -->
    <select id="queryPreviousYearData" resultMap="MaterialAssetYearResultMap">
        SELECT
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        FROM asset_material_year
        WHERE product_category_id = #{productCategoryId}
        AND day = #{timePoint}
        ORDER BY day DESC
        LIMIT 1
    </select>

    <!-- 根据日期范围汇聚日表数据 -->
    <select id="aggregateDayDataByDateRange" resultMap="MaterialAssetDayResultMap">
        SELECT
            product_category_id,
            -- 存量数据：取时间范围内最后一天的数据（截止到该时间点的数据）
            MAX(CASE WHEN day = latest_day THEN all_num END) as all_num,
            MAX(CASE WHEN day = latest_day THEN up_num END) as up_num,
            MAX(CASE WHEN day = latest_day THEN down_num END) as down_num,
            -- 增量数据：对时间范围内的数据求和（累积变化）
            SUM(up_change_num) as up_change_num,
            SUM(down_change_num) as down_change_num,
            SUM(update_num) as update_num
        FROM (
            SELECT
                amd.*,
                (SELECT MAX(day) FROM asset_material_day
                 WHERE day BETWEEN #{startDay} AND #{endDay}
                 AND product_category_id = amd.product_category_id) as latest_day
            FROM asset_material_day amd
            WHERE day BETWEEN #{startDay} AND #{endDay}
        ) t
        GROUP BY product_category_id
    </select>

    <!-- 插入周表数据 -->
    <insert id="insertOrUpdateWeekData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetWeekEntity">
        INSERT INTO asset_material_week (
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        ) VALUES (
            #{id}, #{day}, #{productCategoryId}, #{allNum}, #{upNum}, #{upChangeNum},
            #{downNum}, #{downChangeNum}, #{updateNum}, #{createTime}
        )
    </insert>

    <!-- 更新周表数据 -->
    <update id="updateWeekData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetWeekEntity">
        UPDATE asset_material_week
        SET all_num = #{allNum},
            up_num = #{upNum},
            up_change_num = #{upChangeNum},
            down_num = #{downNum},
            down_change_num = #{downChangeNum},
            update_num = #{updateNum},
            create_time = #{createTime}
        WHERE id = #{id}
    </update>

    <!-- 插入月表数据 -->
    <insert id="insertOrUpdateMonthData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetMonthEntity">
        INSERT INTO asset_material_month (
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        ) VALUES (
            #{id}, #{day}, #{productCategoryId}, #{allNum}, #{upNum}, #{upChangeNum},
            #{downNum}, #{downChangeNum}, #{updateNum}, #{createTime}
        )
    </insert>

    <!-- 更新月表数据 -->
    <update id="updateMonthData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetMonthEntity">
        UPDATE asset_material_month
        SET all_num = #{allNum},
            up_num = #{upNum},
            up_change_num = #{upChangeNum},
            down_num = #{downNum},
            down_change_num = #{downChangeNum},
            update_num = #{updateNum},
            create_time = #{createTime}
        WHERE id = #{id}
    </update>

    <!-- 插入年表数据 -->
    <insert id="insertOrUpdateYearData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetYearEntity">
        INSERT INTO asset_material_year (
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        ) VALUES (
            #{id}, #{day}, #{productCategoryId}, #{allNum}, #{upNum}, #{upChangeNum},
            #{downNum}, #{downChangeNum}, #{updateNum}, #{createTime}
        )
    </insert>

    <!-- 更新年表数据 -->
    <update id="updateYearData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetYearEntity">
        UPDATE asset_material_year
        SET all_num = #{allNum},
            up_num = #{upNum},
            up_change_num = #{upChangeNum},
            down_num = #{downNum},
            down_change_num = #{downChangeNum},
            update_num = #{updateNum},
            create_time = #{createTime}
        WHERE id = #{id}
    </update>

    <!-- 查询周表中是否存在指定数据 -->
    <select id="queryWeekDataByDayAndCategory" resultMap="MaterialAssetWeekResultMap">
        SELECT
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        FROM asset_material_week
        WHERE day = #{day} AND product_category_id = #{productCategoryId}
        LIMIT 1
    </select>

    <!-- 查询月表中是否存在指定数据 -->
    <select id="queryMonthDataByDayAndCategory" resultMap="MaterialAssetMonthResultMap">
        SELECT
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        FROM asset_material_month
        WHERE day = #{day} AND product_category_id = #{productCategoryId}
        LIMIT 1
    </select>

    <!-- 查询年表中是否存在指定数据 -->
    <select id="queryYearDataByDayAndCategory" resultMap="MaterialAssetYearResultMap">
        SELECT
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        FROM asset_material_year
        WHERE day = #{day} AND product_category_id = #{productCategoryId}
        LIMIT 1
    </select>

    <!-- 统计日表数据总数 -->
    <select id="countDayData" resultType="int">
        SELECT COUNT(1)
        FROM asset_material_day
        <where>
            <if test="productCategoryIds != null and productCategoryIds.size() > 0">
                AND product_category_id IN
                <foreach collection="productCategoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
        </where>
    </select>

    <!-- 批量插入日表数据 -->
    <insert id="batchInsertDayData">
        INSERT INTO asset_material_day (
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        ) VALUES
        <foreach collection="dayEntities" item="entity" separator=",">
            (
                #{entity.id}, #{entity.day}, #{entity.productCategoryId}, #{entity.allNum},
                #{entity.upNum}, #{entity.upChangeNum}, #{entity.downNum}, #{entity.downChangeNum},
                #{entity.updateNum}, #{entity.createTime}
            )
        </foreach>
    </insert>

    <!-- 插入或更新日表数据 -->
    <insert id="insertOrUpdateDayData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetDayEntity">
        INSERT INTO asset_material_day (
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        ) VALUES (
            #{id}, #{day}, #{productCategoryId}, #{allNum}, #{upNum}, #{upChangeNum},
            #{downNum}, #{downChangeNum}, #{updateNum}, #{createTime}
        )
    </insert>

    <!-- 查询日表中是否存在指定产品小类和日期的数据 -->
    <select id="queryDayDataByDayAndCategory" resultMap="MaterialAssetDayResultMap">
        SELECT
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        FROM asset_material_day
        WHERE day = #{day} AND product_category_id = #{productCategoryId}
        LIMIT 1
    </select>

    <!-- 更新日表数据 -->
    <update id="updateDayData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetDayEntity">
        UPDATE asset_material_day
        SET all_num = #{allNum},
            up_num = #{upNum},
            up_change_num = #{upChangeNum},
            down_num = #{downNum},
            down_change_num = #{downChangeNum},
            update_num = #{updateNum},
            create_time = #{createTime}
        WHERE id = #{id}
    </update>

    <!-- 插入日表数据 -->
    <insert id="insertDayData" parameterType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetDayEntity">
        INSERT INTO asset_material_day (
            id, day, product_category_id, all_num, up_num, up_change_num,
            down_num, down_change_num, update_num, create_time
        ) VALUES (
            #{id}, #{day}, #{productCategoryId}, #{allNum}, #{upNum}, #{upChangeNum},
            #{downNum}, #{downChangeNum}, #{updateNum}, #{createTime}
        )
    </insert>

    <select id="selectDayDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetDayEntity">
        SELECT day, SUM(all_num) AS all_num, SUM(up_num) AS up_num, SUM(up_change_num) AS up_change_num,
        SUM(down_num) AS down_num, SUM(down_change_num) AS down_change_num, SUM(update_num) AS update_num
        FROM asset_material_day
        WHERE day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day DESC
    </select>

    <select id="selectWeekDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetWeekEntity">
        SELECT day, SUM(all_num) AS all_num, SUM(up_num) AS up_num, SUM(up_change_num) AS up_change_num,
        SUM(down_num) AS down_num, SUM(down_change_num) AS down_change_num, SUM(update_num) AS update_num
        FROM asset_material_week
        WHERE day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day DESC
    </select>

    <select id="selectMonthDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetMonthEntity">
        SELECT day, SUM(all_num) AS all_num, SUM(up_num) AS up_num, SUM(up_change_num) AS up_change_num,
        SUM(down_num) AS down_num, SUM(down_change_num) AS down_change_num, SUM(update_num) AS update_num
        FROM asset_material_month
        WHERE day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day DESC
    </select>

    <select id="selectYearDateByTimeRange" resultType="com.zte.uedm.dcdigital.domain.aggregate.model.entity.MaterialAssetYearEntity">
        SELECT day, SUM(all_num) AS all_num, SUM(up_num) AS up_num, SUM(up_change_num) AS up_change_num,
        SUM(down_num) AS down_num, SUM(down_change_num) AS down_change_num, SUM(update_num) AS update_num
        FROM asset_material_year
        WHERE day BETWEEN #{startTime} AND #{endTime}
        GROUP BY day
        ORDER BY day DESC
    </select>

</mapper>
