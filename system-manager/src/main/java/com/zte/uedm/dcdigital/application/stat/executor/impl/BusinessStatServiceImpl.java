/* Started by AICoder, pid:b6672za32ep2fb31466c0951e1b9a70084d9d162 */
package com.zte.uedm.dcdigital.application.stat.executor.impl;

import com.alibaba.fastjson.JSON;
import com.zte.uedm.dcdigital.application.stat.executor.BusinessStatService;
import com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo;
import com.zte.uedm.dcdigital.common.bean.process.TaskDataInnerVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.domain.utils.StatTimeUtils;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.BusinessStatMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.BusinessStatisticPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticVo;
import com.zte.uedm.dcdigital.sdk.process.service.ProcessService;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BusinessStatServiceImpl implements BusinessStatService {

    @Autowired
    private ProductService productService;

    @Autowired
    private BusinessStatMapper businessStatMapper;

    @Autowired
    private ProcessService processService;

    @Override
    public List<BusinessStatisticVo> statBusiness(BusinessStatisticQueryDto queryDto) {
        /**
         * 统计业务数据。
         *
         * @param queryDto 查询条件传输对象
         * @return 包含统计结果的 BusinessStatisticVo 对象列表
         */
        List<String> categoryIds = productService.selectByCategoryId(queryDto.getProductCategoryId());
        if (null == categoryIds || categoryIds.size() == 0) {
            return Collections.emptyList();
        }
        log.info("categoryIds:{}", JSON.toJSONString(categoryIds));

        List<String> completeTimeRange = StatTimeUtils.generateCompleteTimeRange(
                queryDto.getStartTime(), queryDto.getEndTime(), queryDto.getTimeType());
        log.info("completeTimeRange:{}", JSON.toJSONString(completeTimeRange));
        queryDto.setCompleteTimeRange(completeTimeRange);
        queryDto.setProductCategoryIds(categoryIds);

        List<BusinessStatisticVo> list = getBusinessStatData(queryDto);
        for (BusinessStatisticVo vo : list) {
            switch (queryDto.getTimeType()) {
                case 1: // 天
                    vo.setDay(StatTimeUtils.formatDayDisplay(vo.getDay()));
                    break;
                case 2: // 周
                    vo.setDay(StatTimeUtils.formatWeekDisplay(vo.getDay()));
                    break;
                case 3: // 月
                    vo.setDay(StatTimeUtils.formatMonthDisplay(vo.getDay()));
                    break;
                case 4: // 年
                    vo.setDay(StatTimeUtils.formatYeayDisplay(vo.getDay()));
                    break;
                default:
                    break;
            }

        }
        return list;
    }

    /* Started by AICoder, pid:p92dbvcea6o84d114f2c085d2051bc60519516f9 */
    @Override
    public void synchronizeBusinessStatData(String time) {
        /**
         * 同步业务统计数据。
         * 该方法用于定期同步业务统计数据，确保数据的一致性和准确性。
         */
        Integer previousDayNumber = StatCurrentDateUtils.getPreviousDayNumber();

        List<ProductCategoryInfoVo> list = productService.selectByNodeType(SystemConstants.STR_THREE);
        if (list.size() == 0) {
            return;
        }
        //任务数据
        List<TaskDataInnerVo> taskDataInnerVos = processService.queryTaskStatistics();
        Map<String, List<TaskDataInnerVo>> taskDataInnerVoMap=new HashMap<>();
        if (CollectionUtils.isNotEmpty(taskDataInnerVos)) {
            //转成map<String,List<TaskDataInnerVo>> key为productCategoryId value为taskDataInnerVos
            taskDataInnerVoMap = taskDataInnerVos.stream().collect(Collectors.groupingBy(TaskDataInnerVo::getProductCategoryId));
        }
        // 统计日表
        for (ProductCategoryInfoVo p : list) {
            BusinessStatisticPo po = new BusinessStatisticPo();
            po.setId(UUID.randomUUID().toString());
            po.setProductCategoryId(p.getId());
            po.setDay(String.valueOf(previousDayNumber));
            po.setCreateTime(DateTimeUtils.getCurrentTime());
            BusinessLectotypeStatisticVo vo = productService.demandLectotypeStat(p.getId(), time,1);
            if (null != vo) {
                po.setAllLectotypeNum(vo.getAllLectotypeNum());
                po.setBidLectotypeNum(vo.getBidLectotypeNum());
                po.setAppointLectotypeNum(vo.getAppointLectotypeNum());
            } else {
                po.setAllLectotypeNum(0L);
                po.setBidLectotypeNum(0L);
                po.setAppointLectotypeNum(0L);
            }
            //当前产品小类的总任务数
            long sum = taskDataInnerVoMap.getOrDefault(p.getId(), Collections.emptyList()).stream().mapToLong(TaskDataInnerVo::getAllNum).sum();
            //当前产品小类未延期任务数
            long unPostponedNum = taskDataInnerVoMap.getOrDefault(p.getId(), Collections.emptyList()).stream().mapToLong(TaskDataInnerVo::getUnPostponedNum).sum();
            //当前产品小类延期任务数
            long postponedNum = taskDataInnerVoMap.getOrDefault(p.getId(), Collections.emptyList()).stream().mapToLong(TaskDataInnerVo::getPostponedNum).sum();
            log.info("当前产品小类id:{},当前产品小类:{},总任务数:{},未延期任务数:{},延期任务数:{}", p.getId(),p.getProductName(), sum, unPostponedNum, postponedNum);
            po.setAllTaskNum(sum);
            po.setNormalTaskNum(unPostponedNum);
            po.setExtensionTaskNum(postponedNum);


            businessStatMapper.delBusinessStatDay(po);
            businessStatMapper.addBusinessStatDay(po);

            // 统计周表、月表和年表
            statOther(po);
        }
    }

    /* Started by AICoder, pid:xf113jd01dj17ea14d4d09461061d13c13932a58 */
    @Override
    public BusinessStatisticVo overviewStatBusiness() {
        BusinessStatisticVo po = new BusinessStatisticVo();
        BusinessLectotypeStatisticVo statisticVo = productService.demandLectotypeStatAll();
        if (null != statisticVo) {
            po.setAllLectotypeNum(statisticVo.getAllLectotypeNum());
            po.setBidLectotypeNum(statisticVo.getBidLectotypeNum());
            po.setAppointLectotypeNum(statisticVo.getAppointLectotypeNum());
        } else {
            po.setAllLectotypeNum(0L);
            po.setBidLectotypeNum(0L);
            po.setAppointLectotypeNum(0L);
        }

        return po;
    }
    /* Ended by AICoder, pid:xf113jd01dj17ea14d4d09461061d13c13932a58 */

    private void statOther(BusinessStatisticPo po) {
        /**
         * 统计周表、月表和年表。
         *
         * @param po 业务统计持久化对象
         */
        Integer previousWeekNumber = StatCurrentDateUtils.getPreviousWeekOfYear();
        po.setDay(String.valueOf(previousWeekNumber));
        BusinessLectotypeStatisticVo vo = productService.demandLectotypeStat(po.getProductCategoryId(), String.valueOf(StatCurrentDateUtils.getPreviousDayNumber()),2);
        if (null != vo) {
            po.setAllLectotypeNum(vo.getAllLectotypeNum());
            po.setBidLectotypeNum(vo.getBidLectotypeNum());
            po.setAppointLectotypeNum(vo.getAppointLectotypeNum());
        } else {
            po.setAllLectotypeNum(0L);
            po.setBidLectotypeNum(0L);
            po.setAppointLectotypeNum(0L);
        }
        businessStatMapper.delBusinessStatWeek(po);
        businessStatMapper.addBusinessStatWeek(po);

        Integer previousMonthNumber = StatCurrentDateUtils.getPreviousMonthOfYear();
        po.setDay(String.valueOf(previousMonthNumber));
        BusinessLectotypeStatisticVo vo3 = productService.demandLectotypeStat(po.getProductCategoryId(), String.valueOf(previousMonthNumber),3);
        if (null != vo3) {
            po.setAllLectotypeNum(vo3.getAllLectotypeNum());
            po.setBidLectotypeNum(vo3.getBidLectotypeNum());
            po.setAppointLectotypeNum(vo3.getAppointLectotypeNum());
        } else {
            po.setAllLectotypeNum(0L);
            po.setBidLectotypeNum(0L);
            po.setAppointLectotypeNum(0L);
        }
        businessStatMapper.delBusinessStatMonth(po);
        businessStatMapper.addBusinessStatMonth(po);

        Integer previousYearNumber = StatCurrentDateUtils.getPreviousYear();
        po.setDay(String.valueOf(previousYearNumber));
        BusinessLectotypeStatisticVo vo4 = productService.demandLectotypeStat(po.getProductCategoryId(), String.valueOf(previousYearNumber),4);
        if (null != vo4) {
            po.setAllLectotypeNum(vo4.getAllLectotypeNum());
            po.setBidLectotypeNum(vo4.getBidLectotypeNum());
            po.setAppointLectotypeNum(vo4.getAppointLectotypeNum());
        } else {
            po.setAllLectotypeNum(0L);
            po.setBidLectotypeNum(0L);
            po.setAppointLectotypeNum(0L);
        }
        businessStatMapper.delBusinessStatYear(po);
        businessStatMapper.addBusinessStatYear(po);
    }
    /* Ended by AICoder, pid:p92dbvcea6o84d114f2c085d2051bc60519516f9 */

    private List<BusinessStatisticVo> getBusinessStatData(BusinessStatisticQueryDto queryDto) {
        /**
         * 根据查询条件获取业务统计数据。
         *
         * @param queryDto 查询条件传输对象
         * @return 包含统计结果的 BusinessStatisticVo 对象列表
         */
        switch (queryDto.getTimeType()) {
            case 1: // 天
                return businessStatMapper.selectBusinessStatisticByDay(queryDto);
            case 2: // 周
                return businessStatMapper.selectBusinessStatisticByWeek(queryDto);
            case 3: // 月
                return businessStatMapper.selectBusinessStatisticByMonth(queryDto);
            case 4: // 年
                return businessStatMapper.selectBusinessStatisticByYear(queryDto);
            default:
                return Collections.emptyList();
        }
    }
}
/* Ended by AICoder, pid:b6672za32ep2fb31466c0951e1b9a70084d9d162 */