/* Started by AICoder, pid:999d3wf639fc316143810a1390d6af491a173e37 */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatDayEntity;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatDto;
import java.util.List;

public interface LoginStatDayRepository {
    /**
     * 添加用户登录日统计记录
     *
     * @param loginStatDayEntity 用户登录日统计实体对象
     */
    void addLoginStatDay(LoginStatDayEntity loginStatDayEntity);

    /**
     * 查询用户登录日统计记录
     *
     * @param dayEntity 查询条件
     * @return 用户登录日统计记录列表
     */
    List<LoginStatDayEntity> getLoginStatDayList(LoginStatDayEntity dayEntity);

    /**
     * 检查指定用户是否在目标日期存在登录记录
     *
     * @param userId 用户ID
     * @param date 目标日期（格式：yyyyMMdd）
     * @param deptId 当前用户所在部门id
     * @return true 表示存在登录记录，false 表示无记录
     */
    Boolean recordLoginExist(String userId, Integer date,String deptId);

    /**
     * 获取指定日期范围内的用户登录统计数据（包含周、月、年的登录天数）
     *
     * @param previousDayNumber 前一天的日期
     * @param previousWeekNumber 前一天是当年的第几周
     * @param previousMonthNumber 前一天是当年的第几个月
     * @param previousYearNumber 前一天所在年份
     * @return 用户登录统计数据列表
     */
    List<LoginStatDto> getLoginStatWithDataList(
            Integer previousDayNumber,
            Integer previousWeekNumber,
            Integer previousMonthNumber,
            Integer previousYearNumber
    );
}

/* Ended by AICoder, pid:999d3wf639fc316143810a1390d6af491a173e37 */