package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.PageStatDayRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.PageVisitConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.PageVisitConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.PageStatDayMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.PageStatDayPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageVisitDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class PageStatDayRepositoryImpl implements PageStatDayRepository {
    @Autowired
    private PageStatDayMapper pageStatDayMapper;

    @Override
    public void addPageStatDay(PageStatDayEntity pageStatDayEntity) {
        PageStatDayPo pageStatDayPo= PageVisitConverter.INSTANCE.pageStatDayEntityToPageStatDayPo(pageStatDayEntity);
        pageStatDayMapper.insertPageStatDay(pageStatDayPo);
    }

    @Override
    public List<PageStatDayEntity> getPageStatDayList(PageStatDayEntity dayEntity) {
        PageStatDayPo po=PageVisitConverter.INSTANCE.pageStatDayEntityToPageStatDayPo(dayEntity);
        List<PageStatDayPo> dayPoList=pageStatDayMapper.selectPageStatDayList(po);
        return PageVisitConverter.INSTANCE.pageStatDayPoListToPageStatDayEntityList(dayPoList);
    }

    @Override
    public PageStatDayEntity recordPageExist(String userId,String productCategoryId,String resourceId,String type,String deptId, Integer date) {
        PageStatDayPo po=new PageStatDayPo();
        po.setDay(date);
        po.setUserId(userId);
        po.setResourceId(resourceId);
        po.setDeptId(deptId);
        po.setType(type);
        po.setProductCategoryId(productCategoryId);
        PageStatDayPo dayPo=pageStatDayMapper.selectPageStatDayDataByCondition(po);
        //不存在就返回false，存在就返回true
        return PageVisitConverter.INSTANCE.pageStatDayPoToPageStatDayEntity(dayPo);
    }

    @Override
    public void updatePageStatDay(PageStatDayEntity pageStatDayEntity) {
        PageStatDayPo po=PageVisitConverter.INSTANCE.pageStatDayEntityToPageStatDayPo(pageStatDayEntity);
        pageStatDayMapper.updatePageStatDay(po);
    }

    @Override
    public List<PageVisitDto> getPageVisitStatWithDataList(Integer previousDayNumber, Integer previousWeekNumber, Integer previousMonthNumber, Integer previousYearNumber) {
        return pageStatDayMapper.getPageVisitStatWithDataList(previousDayNumber,previousWeekNumber,previousMonthNumber,previousYearNumber);
    }


}
