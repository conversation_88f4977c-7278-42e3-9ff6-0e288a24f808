package com.zte.uedm.dcdigital.domain.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;

import java.util.List;

/**
 * 物料资产仓储接口
 * 
 * <AUTHOR>
 */
public interface MaterialAssetRepository {

    /**
     * 根据时间类型和时间范围查询物料资产数据
     *
     * @param productCategoryIds 产品小类ID列表
     * @param startTime          开始时间
     * @param endTime            结束时间
     * @param timeType           时间类型：1-天，2-周，3-月，4-年
     * @return 物料资产数据列表
     */
    List<? extends Object> queryMaterialAssetData(List<String> productCategoryIds, String startTime, String endTime, Integer timeType);

    /**
     * 根据时间类型和时间点查询物料资产数据
     *
     * @param productCategoryIds 产品小类ID列表
     * @param timePoint          时间点
     * @param timeType           时间类型：1-天，2-周，3-月，4-年
     * @return 物料资产数据列表
     */
    List<? extends Object> queryMaterialAssetDataByTimePoint(List<String> productCategoryIds, String timePoint, Integer timeType);

    /**
     * 查询前一个时间点的物料资产数据
     *
     * @param productCategoryId 产品小类ID
     * @param timePoint         时间点
     * @param timeType          时间类型：1-天，2-周，3-月，4-年
     * @return 物料资产数据
     */
    Object queryPreviousMaterialAssetData(String productCategoryId, String timePoint, Integer timeType);

    /**
     * 根据日期范围汇聚日表数据
     *
     * @param startDay 开始日期
     * @param endDay   结束日期
     * @return 汇聚后的日表数据列表
     */
    List<MaterialAssetDayEntity> aggregateDayDataByDateRange(String startDay, String endDay);

    /**
     * 插入或更新周表数据
     *
     * @param weekEntity 周表实体
     */
    void insertOrUpdateWeekData(MaterialAssetWeekEntity weekEntity);

    /**
     * 更新周表数据
     *
     * @param weekEntity 周表实体
     */
    void updateWeekData(MaterialAssetWeekEntity weekEntity);

    /**
     * 插入或更新月表数据
     *
     * @param monthEntity 月表实体
     */
    void insertOrUpdateMonthData(MaterialAssetMonthEntity monthEntity);

    /**
     * 更新月表数据
     *
     * @param monthEntity 月表实体
     */
    void updateMonthData(MaterialAssetMonthEntity monthEntity);

    /**
     * 插入或更新年表数据
     *
     * @param yearEntity 年表实体
     */
    void insertOrUpdateYearData(MaterialAssetYearEntity yearEntity);

    /**
     * 更新年表数据
     *
     * @param yearEntity 年表实体
     */
    void updateYearData(MaterialAssetYearEntity yearEntity);

    /**
     * 查询周表中是否存在指定数据
     *
     * @param day               日期
     * @param productCategoryId 产品小类ID
     * @return 周表实体，不存在则返回null
     */
    MaterialAssetWeekEntity queryWeekDataByDayAndCategory(String day, String productCategoryId);

    /**
     * 查询月表中是否存在指定数据
     *
     * @param day               日期
     * @param productCategoryId 产品小类ID
     * @return 月表实体，不存在则返回null
     */
    MaterialAssetMonthEntity queryMonthDataByDayAndCategory(String day, String productCategoryId);

    /**
     * 查询年表中是否存在指定数据
     *
     * @param day               日期
     * @param productCategoryId 产品小类ID
     * @return 年表实体，不存在则返回null
     */
    MaterialAssetYearEntity queryYearDataByDayAndCategory(String day, String productCategoryId);

    /**
     * 检查日表中是否有任何数据
     *
     * @param productCategoryIds 产品小类ID列表
     * @return 是否存在数据
     */
    boolean hasAnyDayData(List<String> productCategoryIds);

    /**
     * 批量插入日表数据
     *
     * @param dayEntities 日表实体列表
     */
    void batchInsertDayData(List<MaterialAssetDayEntity> dayEntities);

    /**
     * 插入或更新日表数据
     *
     * @param dayEntity 日表实体
     */
    void insertOrUpdateDayData(MaterialAssetDayEntity dayEntity);

    /**
     * 查询日表中是否存在指定产品小类和日期的数据
     *
     * @param day               日期
     * @param productCategoryId 产品小类ID
     * @return 日表实体，不存在则返回null
     */
    MaterialAssetDayEntity queryDayDataByDayAndCategory(String day, String productCategoryId);

    /**
     * 更新日表数据
     *
     * @param dayEntity 日表实体
     */
    void updateDayData(MaterialAssetDayEntity dayEntity);

    /**
     * 插入日表数据
     *
     * @param dayEntity 日表实体
     */
    void insertDayData(MaterialAssetDayEntity dayEntity);

    /**
     * 插入或更新物料编辑记录
     * 同一物料同一日期只保留一条记录（在代码中判断）
     *
     * @param updEntity 物料编辑记录实体
     */
    void insertOrUpdateMaterialEditRecord(MaterialAssetUpdEntity updEntity);


    /**
     * 根据日期和产品小类ID统计更新的物料数量
     *
     * @param day               日期
     * @param productCategoryId 产品小类ID
     * @return 更新的物料数量
     */
    Long countUpdatedMaterialsByDayAndCategory(String day, String productCategoryId);

}
