/* Started by AICoder, pid:999d3wf639fc316143810a1390d6af491a173e37 */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.PageStatDayEntity;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatDto;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageVisitDto;

import java.util.List;

public interface PageStatDayRepository {
    /**
     * 添加用户页面访问日统计记录
     *
     * @param pageStatDayEntity 用户页面访问日统计实体对象
     */
    void addPageStatDay(PageStatDayEntity pageStatDayEntity);

    /**
     * 查询用户页面访问日统计记录
     *
     * @param dayEntity 查询条件
     * @return 用户页面访问日统计记录列表
     */
    List<PageStatDayEntity> getPageStatDayList(PageStatDayEntity dayEntity);

    /**
     * 根据条件查询日统计单条记录
     *
     * @param userId 用户ID
     * @param date 目标日期（格式：yyyyMMdd）
     * @param resourceId 品牌Id、物料id等等
     * @param productCategoryId 产品小类id
     * @param type 类型 1：品牌
     * @param deptId 部门id
     * @return true 表示存在页面访问记录，false 表示无记录
     */
    PageStatDayEntity recordPageExist(String userId,String productCategoryId,String resourceId,String type,String deptId, Integer date);

    /**
     * 更新用户页面访问日统计记录
     *
     * @param pageStatDayEntity 用户页面访问日统计实体对象
     */
    void updatePageStatDay(PageStatDayEntity pageStatDayEntity);

    List<PageVisitDto> getPageVisitStatWithDataList(Integer previousDayNumber, Integer previousWeekNumber, Integer previousMonthNumber, Integer previousYearNumber);
}

/* Ended by AICoder, pid:999d3wf639fc316143810a1390d6af491a173e37 */