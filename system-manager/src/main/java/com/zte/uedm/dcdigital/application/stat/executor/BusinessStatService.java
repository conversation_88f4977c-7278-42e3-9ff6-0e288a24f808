/* Started by AICoder, pid:43c30c6717m51d914a190b17d0ca12213697034f */
package com.zte.uedm.dcdigital.application.stat.executor;

import com.zte.uedm.dcdigital.interfaces.web.dto.BusinessStatisticQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.BusinessStatisticVo;

import java.util.List;

/**
 * 业务统计服务接口。
 * 提供业务统计数据的查询和同步功能。
 */
public interface BusinessStatService {

    /**
     * 根据查询条件统计业务数据。
     *
     * @param queryDto 查询条件传输对象
     * @return 包含业务统计数据的 BusinessStatisticVo 对象列表
     */
    List<BusinessStatisticVo> statBusiness(BusinessStatisticQueryDto queryDto);

    /**
     * 同步业务统计数据。
     * 该方法用于定期同步业务统计数据，确保数据的一致性和准确性。
     */
    void synchronizeBusinessStatData(String time);


    BusinessStatisticVo overviewStatBusiness();
}
/* Ended by AICoder, pid:43c30c6717m51d914a190b17d0ca12213697034f */