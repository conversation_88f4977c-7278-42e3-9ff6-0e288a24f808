/* Started by AICoder, pid:f9d3dfc9dfjaa3414bb90bf112757b30d6453dae */
package com.zte.uedm.dcdigital.domain.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.service.LoginStatsDomainService;
import com.zte.uedm.dcdigital.domain.service.strategy.loginstat.LoginStatStrategy;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatQueryDto;
import com.zte.uedm.dcdigital.interfaces.web.vo.LoginStatisticsVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LoginStatsDomainServiceImpl implements LoginStatsDomainService {
    @Autowired
    private LoginStatDayRepository dayRepository;

    @Autowired
    private LoginStatWeekRepository weekRepository;

    @Autowired
    private LoginStatMonthRepository monthRepository;

    @Autowired
    private LoginStatYearRepository yearRepository;
    @Autowired
    private DeptRepository deptRepository;

    private final Map<Integer, LoginStatStrategy> strategyMap;

    // 构造器注入所有策略类
    public LoginStatsDomainServiceImpl(
            @Qualifier("dayLoginStatStrategy") LoginStatStrategy dayStrategy,
            @Qualifier("weekLoginStatStrategy") LoginStatStrategy weekStrategy,
            @Qualifier("monthLoginStatStrategy") LoginStatStrategy monthStrategy,
            @Qualifier("yearLoginStatStrategy") LoginStatStrategy yearStrategy) {

        strategyMap = new HashMap<>();
        strategyMap.put(SystemConstants.ONE, dayStrategy);   // 日
        strategyMap.put(SystemConstants.TWO, weekStrategy);   // 周
        strategyMap.put(SystemConstants.THREE, monthStrategy); // 月
        strategyMap.put(SystemConstants.FOUR, yearStrategy);  // 年
    }

    @Override
    public LoginStatisticsVo calculateSystemAccessLoginStatistics(LoginStatQueryDto queryDto) {
        return executeByTimeType(queryDto);
    }

    @Override
    public void export(LoginStatQueryDto queryDto, HttpServletResponse response) {
        List<LoginStatisticsVo.UserLoginDetail> entityList=executeExportByTimeType(queryDto,response);
        exportUserLoginDetailExcel(response, entityList);
    }

    public List<LoginStatisticsVo.UserLoginDetail> executeExportByTimeType(LoginStatQueryDto dto, HttpServletResponse response) {
        LoginStatStrategy strategy = strategyMap.get(dto.getTimeType());
        if (strategy != null) {
            return strategy.getExported(dto,response);
        } else {
            // 默认导出日表的统计
            return strategyMap.get(SystemConstants.ONE).getExported(dto,response);
        }
    }

    public LoginStatisticsVo executeByTimeType(LoginStatQueryDto dto) {
        LoginStatStrategy strategy = strategyMap.get(dto.getTimeType());
        if (strategy != null) {
            return strategy.handle(dto);
        } else {
            // 默认查询日表的统计
            return strategyMap.get(SystemConstants.ONE).handle(dto);
        }
    }

    @Override
    public void addRecordLogin(String userId) {
        // 获取当前日期并转换为整数格式
        Integer currentDayNumber = StatCurrentDateUtils.getCurrentDayNumber();
        //获取当前登录用户的部门id
        AuthDeptEntity deptInfo = deptRepository.findDeptInfoByUserId(userId);
        // 若未查找到部门id就设置默认值为""
        String deptId = deptInfo == null ? StringUtils.EMPTY : deptInfo.getDeptId();
        // 1. 检查用户是否已存在当日的登录日统计记录  recordLoginExist:true 表示存在登录记录，false 表示无记录
        if (dayRepository.recordLoginExist(userId, currentDayNumber,deptId)) {
            return;
        }

        // 2. 当日首次登录，添加到数据库
        LoginStatDayEntity dayEntity = new LoginStatDayEntity();
        dayEntity.setId(UUID.randomUUID().toString());
        dayEntity.setDay(currentDayNumber);
        //部门id
        dayEntity.setDeptId(deptId);
        dayEntity.setUserId(userId);
        dayEntity.setLoginTime(DateTimeUtils.getCurrentTime());
        dayRepository.addLoginStatDay(dayEntity);
    }
    @Transactional(rollbackFor = Exception.class)
    public void synchronizeLoginStatData(Integer previousDayNumber,
                                         Integer previousWeekNumber,
                                         Integer previousMonthNumber,
                                         Integer previousYearNumber) {

        // 1. 同步周表数据
        syncWeekStat(previousDayNumber, previousWeekNumber);

        // 2. 同步月表数据
        syncMonthStat(previousDayNumber, previousMonthNumber);

        // 3. 同步年表数据
        syncYearStat(previousYearNumber);
    }

    private void syncWeekStat(Integer endDay, Integer weekNumber) {
        // 计算周范围
        Integer startDay = StatCurrentDateUtils.getWeekStartDate(endDay);

        // 查询日表周数据
        LoginStatDayEntity query = new LoginStatDayEntity();
        query.setBeginTime(startDay);
        query.setEndTime(endDay);
        List<LoginStatDayEntity> dayEntities = dayRepository.getLoginStatDayList(query);

        if (CollectionUtils.isEmpty(dayEntities)) {
            log.info("Weekly table synchronization: No daily table data from:{} to:{} was found", startDay, endDay);
            return;
        }

        // 按deptId+userId分组统计登录天数,只检查userId不为空，deptId允许为null
        Map<String, Long> loginCountMap = dayEntities.stream()
                .filter(e -> StringUtils.isNotBlank(e.getUserId())) // 只确保userId不为空
                .collect(Collectors.groupingBy(
                        e -> (e.getDeptId() != null ? e.getDeptId() : "NULL") + "|" + e.getUserId(),
                        Collectors.counting()
                ));

        // 删除原有周表数据
        weekRepository.deleteByWeekNumber(weekNumber);

        // 准备新增数据
        List<LoginStatWeekEntity> weekEntities = loginCountMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("\\|");
                    LoginStatWeekEntity entity = new LoginStatWeekEntity();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setDay(weekNumber);
                    entity.setDeptId("NULL".equals(keys[SystemConstants.ZERO]) ? null : keys[SystemConstants.ZERO]); // 转换回null
                    entity.setUserId(keys[SystemConstants.ONE]);
                    entity.setNum(entry.getValue().intValue());
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量新增
        if (!CollectionUtils.isEmpty(weekEntities)) {
            weekRepository.addLoginStatWeekList(weekEntities);
            log.info("Weekly table Synchronization: Added:{} pieces of data, time range:{} to:{}",
                    weekEntities.size(), startDay, endDay);
        }
    }
    private void syncMonthStat(Integer endDay, Integer monthNumber) {
        // 计算月范围
        Integer startDay = StatCurrentDateUtils.getMonthStartDate(endDay);

        // 查询日表月数据
        LoginStatDayEntity query = new LoginStatDayEntity();
        query.setBeginTime(startDay);
        query.setEndTime(endDay);
        List<LoginStatDayEntity> dayEntities = dayRepository.getLoginStatDayList(query);

        if (CollectionUtils.isEmpty(dayEntities)) {
            log.info("Monthly table synchronization: No daily table data from:{} to:{} was found", startDay, endDay);
            return;
        }

        // 按deptId+userId分组统计登录天数
        Map<String, Long> loginCountMap = dayEntities.stream()
                .filter(e -> StringUtils.isNotBlank(e.getUserId()))
                .collect(Collectors.groupingBy(
                        e -> (e.getDeptId() != null ? e.getDeptId() : "NULL") + "|" + e.getUserId(),
                        Collectors.counting()
                ));

        // 删除原有月表数据
        monthRepository.deleteByMonthNumber(monthNumber);

        // 准备新增数据
        List<LoginStatMonthEntity> monthEntities = loginCountMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("\\|");
                    LoginStatMonthEntity entity = new LoginStatMonthEntity();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setDay(monthNumber);
                    entity.setDeptId("NULL".equals(keys[SystemConstants.ZERO]) ? null : keys[SystemConstants.ZERO]); // 转换回null
                    entity.setUserId(keys[SystemConstants.ONE]);
                    entity.setNum(entry.getValue().intValue());
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量新增
        if (!CollectionUtils.isEmpty(monthEntities)) {
            monthRepository.addLoginStatMonthList(monthEntities);
            log.info("Monthly table Synchronization: Added:{} pieces of data, time range:{} to {}", monthEntities.size(), startDay, endDay);
        }
    }

    private void syncYearStat(Integer yearNumber) {
        // 查询日表年数据
        LoginStatDayEntity query = new LoginStatDayEntity();
        query.setYearTime(yearNumber);
        List<LoginStatDayEntity> dayEntities = dayRepository.getLoginStatDayList(query);

        if (CollectionUtils.isEmpty(dayEntities)) {
            log.info("Chronology synchronization: No daily table data for the year:{} was found", yearNumber);
            return;
        }
        Integer currentDayNumber = StatCurrentDateUtils.getCurrentDayNumber();
        //排除今天的产生的日表数据
        List<LoginStatDayEntity> collect = dayEntities.stream().filter(dayData -> !dayData.getDay().equals(currentDayNumber)).collect(Collectors.toList());
        // 按deptId+userId分组统计登录天数
        Map<String, Long> loginCountMap = collect.stream()
                .filter(e -> StringUtils.isNotBlank(e.getUserId()))
                .collect(Collectors.groupingBy(
                        e -> (e.getDeptId() != null ? e.getDeptId() : "NULL") + "|" + e.getUserId(),
                        Collectors.counting()
                ));

        // 删除原年表数据
        yearRepository.deleteByYearNumber(yearNumber);

        // 准备新增数据
        List<LoginStatYearEntity> yearEntities = loginCountMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("\\|");
                    LoginStatYearEntity entity = new LoginStatYearEntity();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setDay(yearNumber);
                    entity.setDeptId("NULL".equals(keys[SystemConstants.ZERO]) ? null : keys[SystemConstants.ZERO]); // 转换回null
                    entity.setUserId(keys[SystemConstants.ONE]);
                    entity.setNum(entry.getValue().intValue());
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量新增
        if (!CollectionUtils.isEmpty(yearEntities)) {
            yearRepository.addLoginStatYearList(yearEntities);
            log.info("Chronology Synchronization: Added  time range:{}",yearNumber);
        }
    }

    private void exportUserLoginDetailExcel(HttpServletResponse response, List<LoginStatisticsVo.UserLoginDetail> entityList) {
        response.reset();
        response.setHeader("Content-Type", "application/vnd.ms-excel");
        String fileName = "用户登录统计_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xls";
        try {
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);

            try (ServletOutputStream outputStream = response.getOutputStream();
                 ExcelWriter writer = EasyExcel.write(outputStream)
                         .excelType(ExcelTypeEnum.XLS)
                         .charset(StandardCharsets.UTF_8)
                         .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                         .build()) {

                List<String> allPeriods = extractAllPeriods(entityList);
                List<List<String>> headers = buildHeaders(allPeriods);
                List<List<Object>> data = buildData(entityList, allPeriods);

                WriteSheet sheet = EasyExcel.writerSheet("用户登录统计")
                        .head(headers)
                        .build();
                writer.write(data, sheet);
            }
        } catch (IOException e) {
            log.error("导出 Excel 失败", e);
        }
    }


    private List<String> extractAllPeriods(List<LoginStatisticsVo.UserLoginDetail> list) {
        // 使用LinkedHashSet保持插入顺序（按第一个用户的period顺序）
        Set<String> periods = new LinkedHashSet<>();
        if (!list.isEmpty()) {
            // 直接取第一个用户的period顺序作为基准（假设所有用户的period顺序一致）
            list.get(SystemConstants.ZERO).getPeriodLoginStatTimes().forEach(map -> {
                periods.addAll(map.keySet());
            });
        }
        return new ArrayList<>(periods);
    }

    private List<List<String>> buildHeaders(List<String> periods) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Collections.singletonList("用户名"));
        headers.add(Collections.singletonList("总登录次数"));
        for (String period : periods) {
            headers.add(Collections.singletonList(period));
        }
        return headers;
    }

    private List<List<Object>> buildData(List<LoginStatisticsVo.UserLoginDetail> list, List<String> periods) {
        List<List<Object>> rows = new ArrayList<>();
        for (LoginStatisticsVo.UserLoginDetail item : list) {
            List<Object> row = new ArrayList<>();
            row.add(item.getUserName());
            // 动态计算总和
            long dynamicTotal = item.getPeriodLoginStatTimes().stream()
                    .flatMap(m -> m.values().stream())
                    .mapToLong(Long::longValue)
                    .sum();
            row.add(dynamicTotal);
            Map<String, Long> periodMap = item.getPeriodLoginStatTimes().stream()
                    .flatMap(m -> m.entrySet().stream())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            for (String period : periods) {
                row.add(periodMap.getOrDefault(period, 0L));
            }
            rows.add(row);
        }
        return rows;
    }
}

/* Ended by AICoder, pid:f9d3dfc9dfjaa3414bb90bf112757b30d6453dae */