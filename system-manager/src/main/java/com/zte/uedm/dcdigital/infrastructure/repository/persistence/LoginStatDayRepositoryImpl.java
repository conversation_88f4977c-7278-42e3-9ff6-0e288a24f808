package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.entity.LoginStatDayEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.LoginStatDayRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.LoginStatConverter;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.LoginStatDayMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.LoginStatDayPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.LoginStatDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class LoginStatDayRepositoryImpl implements LoginStatDayRepository {
    @Autowired
    private LoginStatDayMapper loginStatDayMapper;

    @Override
    public void addLoginStatDay(LoginStatDayEntity loginStatDayEntity) {
        LoginStatDayPo loginStatDayPo= LoginStatConverter.INSTANCE.dayEntityToLoginStatDayPo(loginStatDayEntity);
        loginStatDayMapper.insertLoginStatDay(loginStatDayPo);
    }

    @Override
    public List<LoginStatDayEntity> getLoginStatDayList(LoginStatDayEntity dayEntity) {
        LoginStatDayPo po=LoginStatConverter.INSTANCE.dayEntityToLoginStatDayPo(dayEntity);
        List<LoginStatDayPo> dayPoList=loginStatDayMapper.selectLoginStatDayList(po);
        return LoginStatConverter.INSTANCE.dayPoListToLoginStatDayEntityList(dayPoList);
    }

    @Override
    public Boolean recordLoginExist(String userId, Integer date,String deptId) {
        LoginStatDayPo po=new LoginStatDayPo();
        po.setDay(date);
        po.setUserId(userId);
        po.setDeptId(deptId);
        List<LoginStatDayPo> dayPoList=loginStatDayMapper.selectLoginStatDayList(po);
        //不存在就返回false，存在就返回true
        return !CollectionUtils.isEmpty(dayPoList);
    }

    @Override
    public List<LoginStatDto> getLoginStatWithDataList(Integer previousDayNumber, Integer previousWeekNumber, Integer previousMonthNumber, Integer previousYearNumber) {
        return loginStatDayMapper.getLoginStatWithDataList(previousDayNumber,previousWeekNumber,previousMonthNumber,previousYearNumber);
    }

}
