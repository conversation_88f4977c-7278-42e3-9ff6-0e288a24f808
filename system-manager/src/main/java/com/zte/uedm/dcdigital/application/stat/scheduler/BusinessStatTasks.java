/* Started by AICoder, pid:876bb4e864i9ba81483f0aca70ca3133a3a1e9b6 */
package com.zte.uedm.dcdigital.application.stat.scheduler;

import com.zte.uedm.dcdigital.application.stat.executor.BusinessStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
public class BusinessStatTasks {
    @Autowired
    private BusinessStatService businessStatService;

    // 开发测试时使用 5分钟执行一次
//     @Scheduled(cron = "0 0/5 * * * ?")

    // 线上暂定每天凌晨1点执行
    @Scheduled(cron = "0 0 2 * * ?")
    public void synchronizeBusinessStatData() {
        /**
         * 同步业务统计数据。
         *
         * 该方法通过定时任务定期调用 `businessStatService.synchronizeBusinessStatData()` 方法，
         * 以同步业务统计数据。日志记录了任务的开始和成功执行。
         */
        log.info("The businessStat scheduled task begins to execute");
        DateTimeFormatter df1 = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDateTime d = LocalDateTime.now().minusDays(1);
        String time = d.format(df1);
        businessStatService.synchronizeBusinessStatData(time);
        log.info("businessStat executed successfully");
    }
}
/* Ended by AICoder, pid:876bb4e864i9ba81483f0aca70ca3133a3a1e9b6 */