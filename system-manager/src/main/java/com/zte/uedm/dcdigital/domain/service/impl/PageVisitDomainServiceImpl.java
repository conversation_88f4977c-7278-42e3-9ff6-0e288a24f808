/* Started by AICoder, pid:sf6f7n1568t95b514b540bf4c06b31757eb237c1 */
package com.zte.uedm.dcdigital.domain.service.impl;

import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.aggregate.model.entity.*;
import com.zte.uedm.dcdigital.domain.aggregate.repository.*;
import com.zte.uedm.dcdigital.domain.common.constant.SystemConstants;
import com.zte.uedm.dcdigital.domain.service.PageVisitDomainService;
import com.zte.uedm.dcdigital.domain.utils.StatCurrentDateUtils;
import com.zte.uedm.dcdigital.interfaces.web.dto.PageVisitPontAddDto;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PageVisitDomainServiceImpl implements PageVisitDomainService {
    @Autowired
    private AuthService authService;

    @Autowired
    private DeptRepository deptRepository;

    @Autowired
    private PageStatDayRepository dayRepository;

    @Autowired
    private PageStatWeekRepository weekRepository;

    @Autowired
    private PageStatMonthRepository monthRepository;

    @Autowired
    private PageStatYearRepository yearRepository;

    @Override
    public void addPageVisitData(PageVisitPontAddDto addDto) {
        // 当前登录用户id
        String currentUserId = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        // 获取当前日期并转换为整数格式
        Integer currentDayNumber = StatCurrentDateUtils.getCurrentDayNumber();
        // 获取当前登录用户部门id
        AuthDeptEntity deptInfo = deptRepository.findDeptInfoByUserId(currentUserId);
        // 若未查找到部门id就设置默认值为""
        String deptId = deptInfo == null ? StringUtils.EMPTY : deptInfo.getDeptId();
        // 判断当前用户在当前日期是否已访问该资源，若访问过则更新，否则新增
        PageStatDayEntity pageStatDayEntity = dayRepository.recordPageExist(
                currentUserId,
                addDto.getProductCategoryId(),
                addDto.getResourceId(),
                addDto.getType(),
                deptId,
                currentDayNumber
        );
        log.info("用户id:{},部门id:{}查询到的访问日表记录:{}",currentUserId,deptId,pageStatDayEntity);
        if (pageStatDayEntity != null) {
            // 更新数据
            pageStatDayEntity.setNum(pageStatDayEntity.getNum() + 1);
            dayRepository.updatePageStatDay(pageStatDayEntity);
            return;
        }

        PageStatDayEntity pageEntity = new PageStatDayEntity();
        pageEntity.setId(UUID.randomUUID().toString());
        pageEntity.setDeptId(deptId);
        pageEntity.setUserId(currentUserId);
        pageEntity.setProductCategoryId(addDto.getProductCategoryId());
        pageEntity.setType(addDto.getType());
        pageEntity.setResourceId(addDto.getResourceId());
        pageEntity.setDay(currentDayNumber);
        pageEntity.setCreateTime(currentTime);
        // 新增默认为1
        pageEntity.setNum(SystemConstants.ONE);
        // 将数据插入到page_stat_day表中
        dayRepository.addPageStatDay(pageEntity);
    }
    @Transactional(rollbackFor = Exception.class)
    public void synchronizePageVisitStatData(Integer previousDayNumber,
                                             Integer previousWeekNumber,
                                             Integer previousMonthNumber,
                                             Integer previousYearNumber) {

        // 1. 同步周表数据
        syncPageVisitToWeekTable(previousDayNumber, previousWeekNumber);

        // 2. 同步月表数据
        syncPageVisitToMonthTable(previousDayNumber, previousMonthNumber);

        // 3. 同步年表数据
        syncPageVisitToYearTable(previousYearNumber);
    }

    // 同步到周表
    private void syncPageVisitToWeekTable(Integer endDay, Integer weekNumber) {
        // 计算周范围
        Integer startDay = StatCurrentDateUtils.getWeekStartDate(endDay);

        // 查询日表数据
        PageStatDayEntity query = new PageStatDayEntity();
        query.setBeginTime(startDay);
        query.setEndTime(endDay);
        List<PageStatDayEntity> dayEntities = dayRepository.getPageStatDayList(query);

        if (CollectionUtils.isEmpty(dayEntities)) {
            log.info("PageVisit Week Sync: No data found:from:{} to:{}", startDay, endDay);
            return;
        }

        // 按复合键分组统计访问次数总和
        Map<String, Integer> visitCountMap = dayEntities.stream()
                .filter(e ->
                        // deptId允许为空，所以不检查
                        StringUtils.isNotBlank(e.getUserId()) &&
                        StringUtils.isNotBlank(e.getType()) &&
                        StringUtils.isNotBlank(e.getResourceId()))
                        // product_category_id允许为null，所以不检查
                .collect(Collectors.groupingBy(
                        this::buildCompositeKey,
                        Collectors.summingInt(PageStatDayEntity::getNum)
                ));

        // 删除原有周表数据（按周数删除）
        weekRepository.deleteByWeekNumber(weekNumber);

        // 准备新增数据
        List<PageStatWeekEntity> weekEntities = visitCountMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("\\|");
                    PageStatWeekEntity entity = new PageStatWeekEntity();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setDay(weekNumber);
                    entity.setDeptId(keys[SystemConstants.ZERO]);
                    entity.setUserId(keys[SystemConstants.ONE]);
                    entity.setProductCategoryId(keys[SystemConstants.TWO]);
                    entity.setType(keys[SystemConstants.THREE]);
                    entity.setResourceId(keys[SystemConstants.FOUR]);
                    entity.setNum(entry.getValue());
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量新增
        if (!CollectionUtils.isEmpty(weekEntities)) {
            weekRepository.addPageStatWeekList(weekEntities);
            log.info("PageVisit Week Sync: Added {} records for week:{}, time range:{} to:{}",
                    weekEntities.size(), weekNumber, startDay, endDay);
        }
    }

    // 同步到月表
    private void syncPageVisitToMonthTable(Integer endDay, Integer monthNumber) {
        // 计算月范围
        Integer startDay = StatCurrentDateUtils.getMonthStartDate(endDay);

        // 查询日表数据
        PageStatDayEntity query = new PageStatDayEntity();
        query.setBeginTime(startDay);
        query.setEndTime(endDay);
        List<PageStatDayEntity> dayEntities = dayRepository.getPageStatDayList(query);

        if (CollectionUtils.isEmpty(dayEntities)) {
            log.info("PageVisit Month Sync: No data found from:{} to:{}", startDay, endDay);
            return;
        }

        // 按复合键分组统计访问次数总和
        Map<String, Integer> visitCountMap = dayEntities.stream()
                .filter(e ->
                        // deptId允许为空，所以不检查
                        StringUtils.isNotBlank(e.getUserId()) &&
                                StringUtils.isNotBlank(e.getType()) &&
                                StringUtils.isNotBlank(e.getResourceId()))
                // product_category_id允许为null，所以不检查
                .collect(Collectors.groupingBy(
                        this::buildCompositeKey,
                        Collectors.summingInt(PageStatDayEntity::getNum)
                ));

        // 删除原有月表数据（按月数删除）
        monthRepository.deleteByMonthNumber(monthNumber);

        // 准备新增数据
        List<PageStatMonthEntity> monthEntities = visitCountMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("\\|");
                    PageStatMonthEntity entity = new PageStatMonthEntity();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setDay(monthNumber);
                    entity.setDeptId(keys[SystemConstants.ZERO]);
                    entity.setUserId(keys[SystemConstants.ONE]);
                    entity.setProductCategoryId(keys[SystemConstants.TWO]);
                    entity.setType(keys[SystemConstants.THREE]);
                    entity.setResourceId(keys[SystemConstants.FOUR]);
                    entity.setNum(entry.getValue());
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量新增
        if (!CollectionUtils.isEmpty(monthEntities)) {
            monthRepository.addPageStatMonthList(monthEntities);
            log.info("PageVisit Month Sync: Added:{} records for month:{}, time range:{} to:{}",
                    monthEntities.size(), monthNumber, startDay, endDay);
        }
    }

    // 同步到年表
    private void syncPageVisitToYearTable(Integer yearNumber) {
        // 查询日表数据
        PageStatDayEntity query = new PageStatDayEntity();
        query.setYearTime(yearNumber);
        List<PageStatDayEntity> dayEntities = dayRepository.getPageStatDayList(query);

        if (CollectionUtils.isEmpty(dayEntities)) {
            log.info("PageVisit Year Sync: No data found for year:{}", yearNumber);
            return;
        }

        // 按复合键分组统计访问次数总和
        Map<String, Integer> visitCountMap = dayEntities.stream()
                .filter(e ->
                        // deptId允许为空，所以不检查
                        StringUtils.isNotBlank(e.getUserId()) &&
                                StringUtils.isNotBlank(e.getType()) &&
                                StringUtils.isNotBlank(e.getResourceId()))
                // product_category_id允许为null，所以不检查
                .collect(Collectors.groupingBy(
                        this::buildCompositeKey,
                        Collectors.summingInt(PageStatDayEntity::getNum)
                ));

        // 删除原年表数据（按年数删除）
        yearRepository.deleteByYearNumber(yearNumber);

        // 准备新增数据
        List<PageStatYearEntity> yearEntities = visitCountMap.entrySet().stream()
                .map(entry -> {
                    String[] keys = entry.getKey().split("\\|");
                    PageStatYearEntity entity = new PageStatYearEntity();
                    entity.setId(UUID.randomUUID().toString());
                    entity.setDay(yearNumber);
                    entity.setDeptId(keys[SystemConstants.ZERO]);
                    entity.setUserId(keys[SystemConstants.ONE]);
                    entity.setProductCategoryId(keys[SystemConstants.TWO]);
                    entity.setType(keys[SystemConstants.THREE]);
                    entity.setResourceId(keys[SystemConstants.FOUR]);
                    entity.setNum(entry.getValue());
                    return entity;
                })
                .collect(Collectors.toList());

        // 批量新增
        if (!CollectionUtils.isEmpty(yearEntities)) {
            yearRepository.addPageStatYearList(yearEntities);
            log.info("PageVisit Year Sync: Added:{} records for year:{}",
                    yearEntities.size(), yearNumber);
        }
    }

    // 复合键构建方法（保持不变）
    private String buildCompositeKey(PageStatDayEntity entity) {
        return String.format("%s|%s|%s|%s|%s",
                entity.getDeptId() != null ? entity.getDeptId() : "NULL",
                entity.getUserId(),
                //没有产品小类id的记录也需要统计
                entity.getProductCategoryId() != null ? entity.getProductCategoryId() : "NULL",
                entity.getType(),
                entity.getResourceId());
    }
}

/* Ended by AICoder, pid:sf6f7n1568t95b514b540bf4c06b31757eb237c1 */