<?xml version="1.0" encoding="UTF-8"?>
<!-- Started by AICoder, pid:k45f309cc9gc57214f330afb01abd4157250b83b -->
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.DeepenDesignFilesRelationMapper">

    <resultMap id="DeepenDesignFilesRelationResult" type="com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo">
        <id property="flowId" column="flow_id"/>
        <result property="itemId" column="item_id"/>
        <result property="productCategoryId" column="product_category_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="submitUser" column="submit_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="approvalResult" column="approval_result"/>
    </resultMap>

    <sql id="selectDeepenDesignFilesRelationVo">
        SELECT flow_id, item_id, product_category_id, create_by, create_time, submit_user, update_time, approval_result FROM deepen_design_files_relation
    </sql>

    <insert id="insertDeepenDesignFilesRelation" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo">
        INSERT INTO deepen_design_files_relation (
        flow_id,
        item_id,
        product_category_id,
        create_by,
        create_time,
        submit_user,
        update_time,
        approval_result
        ) VALUES (
        #{flowId},
        #{itemId},
        #{productCategoryId},
        #{createBy},
        #{createTime},
        #{submitUser},
        #{updateTime},
        #{approvalResult}
        )
    </insert>

    <select id="selectDeepenDesignFilesRelationList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo"
            resultMap="DeepenDesignFilesRelationResult">
        <include refid="selectDeepenDesignFilesRelationVo"/>
        <where>
            <if test="flowId != null and flowId != ''">
                AND flow_id = #{flowId}
            </if>
            <if test="itemId != null and itemId != ''">
                AND item_id = #{itemId}
            </if>
            <if test="productCategoryId != null and productCategoryId != ''">
                AND product_category_id = #{productCategoryId}
            </if>
            <if test="approvalResult != null and approvalResult != ''">
                AND approval_result = #{approvalResult}
            </if>
        </where>
    </select>

    <select id="selectDeepenGroupByItemIdAndCategoryFilesRelationList"
            parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo"
            resultMap="DeepenDesignFilesRelationResult">
        WITH ranked_records AS (
        SELECT *,
        ROW_NUMBER() OVER (
        PARTITION BY item_id, product_category_id
        ORDER BY create_time DESC
        ) AS rn
        FROM public.deepen_design_files_relation
        WHERE item_id = #{itemId}
        )
        SELECT flow_id, item_id, product_category_id, create_by,
        create_time, submit_user, update_time, approval_result
        FROM ranked_records
        WHERE rn = 1
    </select>

    <delete id="deleteDeepenDesignFilesRelationByFlowIds" parameterType="String">
        DELETE FROM deepen_design_files_relation
        WHERE flow_id IN
        <foreach collection="array" item="flowId" open="(" separator="," close=")">
            #{flowId}
        </foreach>
    </delete>

    <select id="selectDeepenDesignFilesRelationByFlowId"
            parameterType="String"
            resultMap="DeepenDesignFilesRelationResult">
        <include refid="selectDeepenDesignFilesRelationVo"/>
        WHERE flow_id = #{flowId}
    </select>

    <update id="updateDeepenDesignFilesRelation" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo">
        UPDATE deepen_design_files_relation
        <set>
            <trim prefixOverrides=",">
                <if test="itemId != null">
                    item_id = #{itemId},
                </if>
                <if test="productCategoryId != null">
                    product_category_id = #{productCategoryId},
                </if>
                <if test="createBy != null">
                    create_by = #{createBy},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime},
                </if>
                <if test="submitUser != null">
                    submit_user = #{submitUser},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime},
                </if>
                <if test="approvalResult != null">
                    approval_result = #{approvalResult},
                </if>
            </trim>
        </set>
        WHERE flow_id = #{flowId}
    </update>

</mapper>

        <!-- Ended by AICoder, pid:k45f309cc9gc57214f330afb01abd4157250b83b -->