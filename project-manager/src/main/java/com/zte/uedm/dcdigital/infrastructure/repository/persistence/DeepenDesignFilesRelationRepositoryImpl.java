/* Started by AICoder, pid:i2c4a00700k51d914df2093bd01e4c30f6f95ac4 */
package com.zte.uedm.dcdigital.infrastructure.repository.persistence;

import com.zte.uedm.dcdigital.domain.aggregate.model.DeepenDesignFilesRelationEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DeepenDesignFilesRelationRepository;
import com.zte.uedm.dcdigital.infrastructure.repository.converter.DeepenDesignFilesRelationConvert;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DeepenDesignFilesRelationMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class DeepenDesignFilesRelationRepositoryImpl implements DeepenDesignFilesRelationRepository {
    @Autowired
    private DeepenDesignFilesRelationMapper filesRelationMapper;

    @Override
    public List<DeepenDesignFilesRelationEntity> selectDeepenDesignFilesRelationListByItemId(String itemId) {
        DeepenDesignFilesRelationPo relationPo = new DeepenDesignFilesRelationPo();
        relationPo.setItemId(itemId);
        List<DeepenDesignFilesRelationPo> deepenDesignFilesRelationPos = filesRelationMapper.selectDeepenDesignFilesRelationList(relationPo);
        return DeepenDesignFilesRelationConvert.INSTANCE.poListToEntityList(deepenDesignFilesRelationPos);
    }

    @Override
    public List<DeepenDesignFilesRelationEntity> selectDeepenGroupByItemIdAndCategoryFilesRelationList(String itemId) {
        DeepenDesignFilesRelationPo relationPo = new DeepenDesignFilesRelationPo();
        relationPo.setItemId(itemId);
        List<DeepenDesignFilesRelationPo> deepenDesignFilesRelationPos = filesRelationMapper.selectDeepenGroupByItemIdAndCategoryFilesRelationList(relationPo);
        return DeepenDesignFilesRelationConvert.INSTANCE.poListToEntityList(deepenDesignFilesRelationPos);
    }

    @Override
    public int addDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity) {
        DeepenDesignFilesRelationPo relationPo = DeepenDesignFilesRelationConvert.INSTANCE.entityToPo(deepenDesignFilesRelationEntity);
        return filesRelationMapper.insertDeepenDesignFilesRelation(relationPo);
    }

    @Override
    public int updateDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity) {
        DeepenDesignFilesRelationPo relationPo = DeepenDesignFilesRelationConvert.INSTANCE.entityToPo(deepenDesignFilesRelationEntity);
        return filesRelationMapper.updateDeepenDesignFilesRelation(relationPo);
    }

    @Override
    public List<DeepenDesignFilesRelationEntity> queryDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity) {
        DeepenDesignFilesRelationPo relationPo = DeepenDesignFilesRelationConvert.INSTANCE.entityToPo(deepenDesignFilesRelationEntity);
        List<DeepenDesignFilesRelationPo> poList=filesRelationMapper.selectDeepenDesignFilesRelationList(relationPo);
        if (CollectionUtils.isEmpty(poList)){
            return Collections.emptyList();
        }
        return DeepenDesignFilesRelationConvert.INSTANCE.poListToEntityList(poList);
    }
}

/* Ended by AICoder, pid:i2c4a00700k51d914df2093bd01e4c30f6f95ac4 */