package com.zte.uedm.dcdigital.interfaces.web.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 深化实施信息VO
 */
@Getter
@Setter
@ToString
public class DeepenImplementationVo {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 设计负责人
     */
    private String designDirector;

    /**
     * 设计负责人ID
     */
    private String designDirectorId;

    /**
     * 启动时间
     */
    private String startTime;

    /**
     * 要求提资完成时间
     */
    private String requestInformationCompletionTime;

    /**
     * 实际提资完成时间
     */
    private String actualInformationCompletionTime;

    /**
     * 深化设计开始时间
     */
    private String deepenDesignStartTime;

    /**
     * 深化设计结束时间
     */
    private String deepensDesignEndTime;

    /**
     * 合图开始时间
     */
    private String combinedImageStartTime;

    /**
     * 合图结束时间
     */
    private String combinedImageEndTime;

    /**
     * BIM专项设计开始时间
     */
    private String bIMSpecialDesignStartTime;

    /**
     * BIM专项设计结束时间
     */
    private String bIMSpecialDesignEndTime;

    /**
     * 首批备料清单输出时间
     */
    private String firstBatchMaterialPreparationOutputTime;

    /**
     * 长周期物料清单输出时间
     */
    private String longPeriodMaterialListOutputTime;

    /**
     * 整体备料清单锁定时间
     */
    private String overallMaterialPreparationLockTime;

    /**
     * 工程深化进度，0-100
     */
    private Integer engineeringDeepenProgress;

    /**
     * 深化设计状态
     */
    private DeepenDesignStatusVo deepenDesignStatus;

    /**
     * 延误原因
     */
    private String delayReason;

    /**
     * 修改深化设计开始时间
     */
    private String modifyDeepenDesignStartTime;

    /**
     * 修改深化设计结束时间
     */
    private String modifyDeepenDesignEndTime;

    /**
     * 施工图校正开始时间
     */
    private String constructionDrawingCalibrationStartTime;

    /**
     * 施工图校正结束时间
     */
    private String constructionDrawingCalibrationEndTime;

    /**
     * 施工图修订开始时间
     */
    private String constructionDrawingRevisionStartTime;

    /**
     * 深化设计状态VO
     */
    @Getter
    @Setter
    @ToString
    public static class DeepenDesignStatusVo {
        /**
         * 状态枚举ID或Code
         */
        private String id;

        /**
         * 描述
         */
        private String name;
    }
}
