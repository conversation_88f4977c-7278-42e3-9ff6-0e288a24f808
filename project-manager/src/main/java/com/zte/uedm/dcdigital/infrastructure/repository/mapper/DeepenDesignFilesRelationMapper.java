/* Started by AICoder, pid:pf4a5c728420eef14f16089a30dffd590c6199db */
package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenDesignFilesRelationPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DeepenDesignFilesRelationMapper extends BaseMapper<DeepenDesignFilesRelationPo> {

    /**
     * 插入深化设计文件关系记录
     *
     * @param deepenDesignFilesRelationPo 深化设计文件关系对象
     * @return 影响行数
     */
    int insertDeepenDesignFilesRelation(DeepenDesignFilesRelationPo deepenDesignFilesRelationPo);

    /**
     * 查询深化设计文件关系列表（支持条件查询）
     *
     * @param deepenDesignFilesRelationPo 查询条件封装对象
     * @return 符合条件的深化设计文件关系列表
     */
    List<DeepenDesignFilesRelationPo> selectDeepenDesignFilesRelationList(DeepenDesignFilesRelationPo deepenDesignFilesRelationPo);

    List<DeepenDesignFilesRelationPo> selectDeepenGroupByItemIdAndCategoryFilesRelationList(DeepenDesignFilesRelationPo deepenDesignFilesRelationPo);

    /**
     * 批量删除深化设计文件关系记录
     *
     * @param flowIds 需要删除的flowId数组
     * @return 影响行数
     */
    int deleteDeepenDesignFilesRelationByFlowIds(String[] flowIds);

    /**
     * 根据主键ID查询深化设计文件关系
     *
     * @param flowId 主键ID
     * @return 对应的深化设计文件关系对象
     */
    DeepenDesignFilesRelationPo selectDeepenDesignFilesRelationByFlowId(String flowId);

    /**
     * 更新深化设计文件关系记录
     *
     * @param deepenDesignFilesRelationPo 包含更新信息的深化设计文件关系对象
     * @return 影响行数
     */
    int updateDeepenDesignFilesRelation(DeepenDesignFilesRelationPo deepenDesignFilesRelationPo);
}

/* Ended by AICoder, pid:pf4a5c728420eef14f16089a30dffd590c6199db */