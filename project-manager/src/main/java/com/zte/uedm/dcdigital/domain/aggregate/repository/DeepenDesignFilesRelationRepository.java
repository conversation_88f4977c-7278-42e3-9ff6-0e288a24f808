/* Started by AICoder, pid:560b224a194305514a9c09d6f066ce38e4e08ff1 */
package com.zte.uedm.dcdigital.domain.aggregate.repository;

import com.zte.uedm.dcdigital.domain.aggregate.model.DeepenDesignFilesRelationEntity;
import java.util.List;

public interface DeepenDesignFilesRelationRepository {
    /**
     * 根据项目ID查询产品提资关联流程信息
     *
     * @param itemId 项目唯一标识
     * @return 匹配的关联流程信息列表
     */
    List<DeepenDesignFilesRelationEntity> selectDeepenDesignFilesRelationListByItemId(String itemId);

    /**
     * 根据项目ID查询产品提资关联流程信息
     *
     * @param itemId 项目唯一标识
     * @return 匹配的关联流程信息列表
     */
    List<DeepenDesignFilesRelationEntity> selectDeepenGroupByItemIdAndCategoryFilesRelationList(String itemId);

    /**
     * 新增产品提资关联流程信息
     *
     * @param deepenDesignFilesRelationEntity 关联流程实体对象
     * @return 影响行数
     */
    int addDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity);

    /**
     * 更新产品提资关联流程信息
     *
     * @param deepenDesignFilesRelationEntity 关联流程实体对象
     * @return 影响行数
     */
    int updateDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity);

    /**
     * 查询产品提资关联流程信息
     *
     * @param deepenDesignFilesRelationEntity 关联流程实体对象
     * @return List<DeepenDesignFilesRelationEntity>
     */
    List<DeepenDesignFilesRelationEntity> queryDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity);
}

/* Ended by AICoder, pid:560b224a194305514a9c09d6f066ce38e4e08ff1 */