/* Started by AICoder, pid:n3387k8f67f62d5145190b31003e6c7062939889 */
package com.zte.uedm.dcdigital.domain.utils;

import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
public class DateUtils {
    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 解析日期字符串为LocalDate对象
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return 解析成功的LocalDate对象，解析失败返回null
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    /**
     * 判断任务是否延期（修复版）
     * @param actualCompleteDate 实际完成时间（可接受 yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss）
     * @param dueDate 规定完成时间（可接受 yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss）
     * @param currentDate 当前日期（yyyy-MM-dd）
     */
    public static boolean isDelayed(String actualCompleteDate, String dueDate, String currentDate) {
        if (dueDate == null || dueDate.trim().isEmpty()) {
            return false;
        }

        LocalDate actual = parseDateSafety(extractDatePart(actualCompleteDate));
        LocalDate due = parseDateSafety(extractDatePart(dueDate));
        LocalDate current = parseDateSafety(currentDate);

        // 如果 due 解析失败，无法判断，返回 false
        if (due == null) {
            return false;
        }

        if (actual != null) {
            return actual.isAfter(due);
        } else {
            // 如果 current 也解析不了，无法判断是否延期
            if (current == null) {
                log.warn("当前日期解析失败，无法判断是否延期: {}", currentDate);
                return false;
            }
            return current.isAfter(due);
        }
    }

    /**
     * 从字符串中提取 yyyy-MM-dd 部分
     */
    private static String extractDatePart(String datetime) {
        if (datetime == null) return null;
        return datetime.split(" ")[0]; // 截取空格前的日期部分
    }

    /**
     * 安全解析日期
     */
    private static LocalDate parseDateSafety(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr);
        } catch (Exception e) {
            log.warn("日期解析失败: {}", dateStr);
            return null; // 不抛异常，返回 null 表示失败
        }
    }

    /**
     * 将完整时间格式转换为短日期格式
     *
     * @param dateTimeStr 原始时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @return 格式化后的日期字符串（yyyy-MM-dd），解析失败返回null
     */
    public static String formatToShortDate(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, INPUT_FORMATTER);
            return dateTime.format(OUTPUT_FORMATTER);
        } catch (DateTimeParseException e) {
            log.error("时间格式解析失败：{}", dateTimeStr);
            throw new BusinessException(StatusCode.PARAM_ERROR);
        }
    }

    /**
     * 获取当前日期的前一天，格式为 yyyy-MM-dd
     */
    public static String getPreviousDate() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        return yesterday.format(DATE_FORMATTER);
    }

}

/* Ended by AICoder, pid:n3387k8f67f62d5145190b31003e6c7062939889 */