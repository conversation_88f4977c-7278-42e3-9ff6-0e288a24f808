<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.dcdigital.infrastructure.repository.mapper.DemandMapper">

    <!-- Started by AICoder, pid:s2530k503ewd1fd1453009db108a772b20f63f89 -->
    <insert id="addDemand" parameterType="com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementDto">
        <!--
            插入新的需求管理记录到 demand_management 表中。

            参数类型为 com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementDto，
            该对象包含所有需要插入的字段值。
        -->
        INSERT INTO demand_management
        (id, project_id, bill_quantity_id, product_category_id, create_user, exp_time_complet, act_time_complet,
        processor, demand_type, create_by, create_time, update_by, update_time,bill_quantitie_time)
        VALUES(
        #{id},               <!-- 需求管理记录的唯一标识符 -->
        #{projectId},        <!-- 项目ID -->
        #{billQuantityId},  <!-- 账单数量ID -->
        #{productCategoryId},<!-- 产品类别ID -->
        #{createUser},       <!-- 创建用户 -->
        #{expTimeComplet},   <!-- 预计完成时间 -->
        #{actTimeComplet},   <!-- 实际完成时间 -->
        #{processor},        <!-- 处理人 -->
        #{demandType},       <!-- 需求类型 -->
        #{createBy},         <!-- 创建者 -->
        #{createTime},       <!-- 创建时间 -->
        #{updateBy},         <!-- 更新者 -->
        #{updateTime},        <!-- 更新时间 -->
        #{billQuantitieTime}        <!-- 更新时间 -->
        )
    </insert>
    <!-- Ended by AICoder, pid:s2530k503ewd1fd1453009db108a772b20f63f89 -->

    <update id="updDemand" parameterType="com.zte.uedm.dcdigital.infrastructure.repository.po.DemandManagementPo">

        UPDATE demand_management
        <set >
            <if test="billQuantityId != null" >
                bill_quantity_id = #{billQuantityId},
            </if>
            <if test="expTimeComplet != null" >
                exp_time_complet = #{expTimeComplet},
            </if>
            <if test="actTimeComplet != null" >
                act_time_complet = #{actTimeComplet},
            </if>
            <if test="processor != null" >
                processor = #{processor},
            </if>
            <if test="demandType != null" >
                demand_type = #{demandType},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null" >
                update_by = #{updateBy},
            </if>
            <if test="billQuantitieTime != null" >
                bill_quantitie_time = #{billQuantitieTime}
            </if>
        </set>
        where  id=#{id}
    </update>

    <!-- Started by AICoder, pid:aa1f9g9140tf30f14c5b0a270056014667259dff -->
    <resultMap id="demandManagementVo" type="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementVo">
        <result property="id" column="id"/> <!-- 映射ID字段 -->
        <result property="projectId" column="project_id"/> <!-- 映射项目ID字段 -->
        <result property="billQuantityId" column="bill_quantity_id"/> <!-- 映射账单数量ID字段 -->
        <result property="productCategoryId" column="product_category_id"/> <!-- 映射产品类别ID字段 -->
        <result property="createUser" column="create_user"/> <!-- 映射创建用户字段 -->
        <result property="expTimeComplet" column="exp_time_complet"/> <!-- 映射预计完成时间字段 -->
        <result property="actTimeComplet" column="act_time_complet"/> <!-- 映射实际完成时间字段 -->
        <result property="processor" column="processor"/> <!-- 映射处理人字段 -->
        <result property="demandType" column="demand_type"/> <!-- 映射需求类型字段 -->
        <result property="createBy" column="create_by"/> <!-- 映射创建者字段 -->
        <result property="createTime" column="create_time"/> <!-- 映射创建时间字段 -->
        <result property="updateBy" column="update_by"/> <!-- 映射更新者字段 -->
        <result property="updateTime" column="update_time"/> <!-- 映射更新时间字段 -->
        <result property="billQuantitieTime" column="bill_quantitie_time"/> <!-- 映射变更时间字段 -->
    </resultMap>

    <select id="pageDemand" resultMap="demandManagementVo" parameterType="com.zte.uedm.dcdigital.interfaces.web.material.dto.PageDemandDto">
        <!-- 查询需求管理记录，支持分页和条件过滤 -->
        select * from demand_management
        <where>
            <!-- 条件过滤 -->
            <if test="projectId != null and projectId != ''">
                and project_id = #{projectId} <!-- 过滤项目ID -->
            </if>
            <if test="productCategoryId != null and productCategoryId != ''">
                and product_category_id = #{productCategoryId} <!-- 过滤产品类别ID -->
            </if>
            <if test="demandType != null and demandType != ''">
                and demand_type = #{demandType} <!-- 过滤需求类型 -->
            </if>
            <if test="expTimeCompletStart != null and expTimeCompletStart != ''">
                and exp_time_complet >= #{expTimeCompletStart} <!-- 过滤预计完成时间起始 -->
            </if>
            <if test="expTimeCompletEnd != null and expTimeCompletEnd != ''">
                and exp_time_complet &lt;= #{expTimeCompletEnd} <!-- 过滤预计完成时间结束 -->
            </if>
            <if test="processor != null and processor != ''">
                and processor = #{processor} <!-- 过滤处理人 -->
            </if>
            <if test="billQuantityId != null and billQuantityId != ''">
                and bill_quantity_id = #{billQuantityId} <!-- 过滤账单数量ID -->
            </if>
        </where>
        order by ${orderField} ${direction} <!-- 排序 -->
    </select>
    <!-- Ended by AICoder, pid:aa1f9g9140tf30f14c5b0a270056014667259dff -->

    <!-- Started by AICoder, pid:qc7c353e457a08e147950ba56181566631b631d8 -->
    <resultMap id="demandManagementLectotypeVo" type="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo">
        <result property="lectotypeId" column="lectotype_id"/> <!-- 映射定标ID字段 -->
        <result property="demandId" column="demand_id"/> <!-- 映射需求ID字段 -->
        <result property="lectotypeName" column="lectotype_name"/> <!-- 映射定标名称字段 -->
        <result property="lectotypeType" column="lectotype_type"/> <!-- 映射定标类型字段 -->
        <result property="lectotypeStatus" column="lectotype_status"/> <!-- 映射定标状态字段 -->
        <result property="sendBidTime" column="send_bid_time"/> <!-- 映射发送投标时间字段 -->
        <result property="endTime" column="end_time"/> <!-- 映射结束时间字段 -->
        <result property="openBidTime" column="open_bid_time"/> <!-- 映射开标时间字段 -->
        <result property="bidUrl" column="bid_url"/> <!-- 映射投标URL字段 -->
        <result property="createBy" column="create_by"/> <!-- 映射创建者字段 -->
        <result property="createTime" column="create_time"/> <!-- 映射创建时间字段 -->
        <result property="updateBy" column="update_by"/> <!-- 映射更新者字段 -->
        <result property="updateTime" column="update_time"/> <!-- 映射更新时间字段 -->
        <result property="fileIds" column="file_ids"/> <!-- 映射更新时间字段 -->
    </resultMap>

    <select id="pageLectotype" resultMap="demandManagementLectotypeVo">
        <!--
            查询指定需求ID的定标记录。

            参数：
            - demandId: 需求ID
        -->
        select * from demand_management_lectotype where demand_id=#{demandId}  order by create_time desc
    </select>

    <select id="getLectotypeById" resultMap="demandManagementLectotypeVo">
        <!--
            查询指定需求ID的定标记录。

            参数：
            - demandId: 需求ID
        -->
        select * from demand_management_lectotype where lectotype_id=#{lectotypeId}
    </select>

    <resultMap id="demandManagementPo" type="com.zte.uedm.dcdigital.infrastructure.repository.po.DemandManagementPo">
        <result property="id" column="id"/> <!-- 映射ID字段 -->
        <result property="projectId" column="project_id"/> <!-- 映射项目ID字段 -->
        <result property="billQuantityId" column="bill_quantity_id"/> <!-- 映射账单数量ID字段 -->
        <result property="productCategoryId" column="product_category_id"/> <!-- 映射产品类别ID字段 -->
        <result property="createUser" column="create_user"/> <!-- 映射创建用户字段 -->
        <result property="expTimeComplet" column="exp_time_complet"/> <!-- 映射预计完成时间字段 -->
        <result property="actTimeComplet" column="act_time_complet"/> <!-- 映射实际完成时间字段 -->
        <result property="processor" column="processor"/> <!-- 映射处理人字段 -->
        <result property="demandType" column="demand_type"/> <!-- 映射需求类型字段 -->
        <result property="createBy" column="create_by"/> <!-- 映射创建者字段 -->
        <result property="createTime" column="create_time"/> <!-- 映射创建时间字段 -->
        <result property="updateBy" column="update_by"/> <!-- 映射更新者字段 -->
        <result property="updateTime" column="update_time"/> <!-- 映射更新时间字段 -->
        <result property="billQuantitieTime" column="bill_quantitie_time"/> <!-- 映射变更时间字段 -->
    </resultMap>

    <select id="selectDemandById" resultMap="demandManagementPo">
        <!--
            根据ID查询需求管理记录。

            参数：
            - demandId: 需求ID
        -->
        select * from demand_management where id=#{demandId}
    </select>

    <insert id="addLectotype" parameterType="com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementLectotypeDto">
        <!--
            插入新的定标记录到 demand_management_lectotype 表中。

            参数：
            - lectotypeId: 定标ID
            - demandId: 需求ID
            - lectotypeName: 定标名称
            - lectotypeType: 定标类型
            - lectotypeStatus: 定标状态
            - sendBidTime: 发送投标时间
            - openBidTime: 开标时间
            - endTime: 结束时间
            - bidUrl: 投标URL
            - createBy: 创建者
            - createTime: 创建时间
            - updateBy: 更新者
            - updateTime: 更新时间
        -->
        INSERT INTO demand_management_lectotype
        (lectotype_id, demand_id, lectotype_name, lectotype_type, lectotype_status, send_bid_time, open_bid_time,
        end_time, bid_url, file_ids, create_by, create_time, update_by, update_time)
        VALUES(#{lectotypeId}, #{demandId}, #{lectotypeName}, #{lectotypeType},#{lectotypeStatus},#{sendBidTime},#{openBidTime},
        #{endTime},  #{bidUrl}, #{fileIds}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime})
    </insert>

    <update id="updLectotype" parameterType="com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto">
        <!--
            更新定标记录。

            参数：
            - lectotypeId: 定标ID
            - lectotypeName: 定标名称
            - lectotypeType: 定标类型
            - lectotypeStatus: 定标状态
            - sendBidTime: 发送投标时间
            - openBidTime: 开标时间
            - endTime: 结束时间
            - bidUrl: 投标URL
            - updateTime: 更新时间
            - updateBy: 更新者
        -->
        UPDATE demand_management_lectotype
        <set >
            <if test="lectotypeName != null" >
                lectotype_name = #{lectotypeName},
            </if>
            <if test="lectotypeType != null" >
                lectotype_type = #{lectotypeType},
            </if>
            <if test="lectotypeStatus != null" >
                lectotype_status = #{lectotypeStatus},
            </if>
            <if test="sendBidTime != null" >
                send_bid_time = #{sendBidTime},
            </if>
            <if test="openBidTime != null" >
                open_bid_time = #{openBidTime},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime},
            </if>
            <if test="bidUrl != null" >
                bid_url = #{bidUrl},
            </if>
            <if test="fileIds != null" >
                file_ids = #{fileIds},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null" >
                update_user = #{updateBy}
            </if>
        </set>
        where  lectotype_id=#{lectotypeId}
    </update>

    <update id="updLectotypeStatus" parameterType="com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto">

        UPDATE demand_management_lectotype
        <set >
            <if test="lectotypeStatus != null" >
                lectotype_status = #{lectotypeStatus},
            </if>
            <if test="bidUrl != null" >
                bid_url = #{bidUrl},
            </if>
            <if test="endTime != null" >
                end_time = #{endTime},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null" >
                update_by = #{updateBy}
            </if>
        </set>
        where  lectotype_id=#{lectotypeId}
    </update>

    <update id="updLectotypeByDemandId">
        <!-- Started by AICoder, pid:jb211e5b94ic589145920b6e002c381554b5ffc2 -->
        UPDATE demand_management_lectotype
        <set>
            <if test="lectotypeStatus != null">
                lectotype_status = #{lectotypeStatus},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy}
            </if>
        </set>
        WHERE demand_id = #{demandId}
    </update>

    <!-- Ended by AICoder, pid:jb211e5b94ic589145920b6e002c381554b5ffc2 -->

    <delete id="delLectotype">
        <!--
            删除指定的定标记录。

            参数：
            - lectotypeId: 定标ID
        -->
        delete from demand_management_lectotype where lectotype_id=#{lectotypeId}
    </delete>

    <select id="getLectotypeMaterialByLtc" resultType="string">
        <!--
            查询指定定标ID的物料ID列表。

            参数：
            - lectotypeId: 定标ID
        -->
        select material_id from  demand_management_lectotype_material where lectotype_id=#{lectotypeId} order by create_time desc
    </select>
    <select id="doLectotypeExist" resultType="java.lang.Boolean">
        SELECT EXISTS (SELECT 1 FROM demand_management_lectotype WHERE lectotype_id= #{id})
    </select>
    <select id="queryByCondition"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.SelectionFormVo">
        SELECT
        dml.lectotype_id as id,
        dml.lectotype_name as name,
        dm.product_category_id,
        dml.lectotype_type,
        dm.processor,
        dml.lectotype_status,
        dml.send_bid_time,
        dml.open_bid_time,
        dml.end_time as completionTime,
        dml.create_time,
        dm.bill_quantitie_time
        FROM
        demand_management AS dm
        JOIN
        demand_management_lectotype AS dml ON dm.id = dml.demand_id
        <where>
            <if test="projectIds != null and !projectIds.isEmpty()">
                dm.project_id IN
                <foreach collection="projectIds" item="project" open="(" separator="," close=")">
                    #{project}
                </foreach>
            </if>
            <if test="statusList != null and !statusList.isEmpty()">
                AND dml.lectotype_status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="typeList != null and !typeList.isEmpty()">
                AND dml.lectotype_type IN
                <foreach collection="typeList" item="lecType" open="(" separator="," close=")">
                    #{lecType}
                </foreach>
            </if>
            <if test="productCategoryIdList != null and !productCategoryIdList.isEmpty()">
                AND dm.product_category_id IN
                <foreach collection="productCategoryIdList" item="category" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>
            <if test="selectionName != null and selectionName != ''">
                AND dml.lectotype_name ILIKE CONCAT('%', #{selectionName}, '%')
            </if>
        </where>
        ORDER BY dml.create_time
    </select>
    <select id="getLectotypeMaterialByMaterialIds"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.LectotypeMaterialVo">
        select
        dml.lectotype_id,
        lm.material_id,
        dml.lectotype_status,
        dml.lectotype_type
        from  demand_management_lectotype dml
        JOIN
        demand_management_lectotype_material AS lm ON lm.lectotype_id = dml.lectotype_id
        <where>
            <if test="ids != null and !ids.isEmpty()">
                lm.material_id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="delLectotypeMaterialByLec">
        <!--
            删除指定定标ID的所有物料记录。

            参数：
            - lectotypeId: 定标ID
        -->
        delete from demand_management_lectotype_material where lectotype_id=#{lectotypeId}
    </delete>

    <delete id="delLectotypeMaterialByMat">
        <!--
            删除指定物料ID的所有定标记录。

            参数：
            - materialId: 物料ID
        -->
        delete from demand_management_lectotype_material where material_id=#{materialId}
    </delete>
    <!-- Ended by AICoder, pid:qc7c353e457a08e147950ba56181566631b631d8 -->

    <!-- Started by AICoder, pid:yd8c5g95e051668148480ba7a028ef2e01b1cbc0 -->
    <insert id="addLectotypeMaterial">
        <!--
            插入新的定标物料记录到 demand_management_lectotype_material 表中。

            参数：
            - lectotypeId: 定标ID
            - materialId: 物料ID
        -->
        insert into demand_management_lectotype_material(lectotype_id, material_id,create_time)
        values (#{lectotypeId}, #{materialId}, #{createTime})
    </insert>

    <delete id="delLectotypeMaterial">
        <!--
            从 demand_management_lectotype_material 表中删除指定的定标物料记录。

            参数：
            - lectotypeId: 定标ID
            - materialId: 物料ID
        -->
        delete from demand_management_lectotype_material where lectotype_id=#{lectotypeId} and material_id=#{materialId}
    </delete>
    <delete id="batchDelLectotypeMaterial">
        delete from demand_management_lectotype_material where lectotype_id = #{lectotypeId}
        <if test="materialIdList != null and !materialIdList.isEmpty()">
            and material_id IN
            <foreach collection="materialIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>
    <!-- Ended by AICoder, pid:yd8c5g95e051668148480ba7a028ef2e01b1cbc0 -->
    <select id="getLectotypeByMaterialId" resultMap="demandManagementLectotypeVo">

        select * from demand_management_lectotype

        where lectotype_id in (select lectotype_id from demand_management_lectotype_material where  material_id=#{materialId}) limit 1
    </select>

    <!-- Started by AICoder, pid:45e04p02042306e14d85083f507bfd132494aafc -->
    <select id="getProductIdByMaterialId" resultType="string">
        select project_id from demand_management where id in (
        select demand_id from demand_management_lectotype  where lectotype_id in (
        select lectotype_id from demand_management_lectotype_material where material_id=#{materialId}
        )
        )
    </select>
    <select id="queryDemandManagement"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementVo">
        select * from demand_management
        where project_id = #{projectId} and product_category_id = #{productCategoryId}
    </select>
    <select id="queryEarliestByDemandId"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo">
        select * from demand_management_lectotype where demand_id = #{id}
        and lectotype_type = #{type} order by create_time limit 1
    </select>
    <select id="queryDemandByProjectAndCategoryIds"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementVo">
        select * from demand_management
        where project_id = #{projectId}
        and product_category_id IN
        <foreach collection="categoryIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="queryLectotypeByDemandIdList"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo">
        select * from demand_management_lectotype
        where demand_id IN
        <foreach collection="demandIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="type!=null and type!=''">
            AND lectotype_type = #{type}
        </if>
    </select>
    <select id="queryLastByDemandId"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo">
        select * from demand_management_lectotype where demand_id = #{id}
        order by create_time desc limit 1
    </select>
    <select id="queryRelationMaterialByIds"
            resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.LectotypeMaterialVo">
        select
        dml.lectotype_id,
        dml.material_id
        from  demand_management_lectotype_material dml
        where dml.lectotype_id IN
        <foreach collection="lectotypeIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <!-- Ended by AICoder, pid:45e04p02042306e14d85083f507bfd132494aafc -->

    <select id="getDemandManagementLectotypeById" resultMap="demandManagementPo">
        select * from demand_management where id in (
        select demand_id from demand_management_lectotype where lectotype_id=#{lectotypeId}
        )
    </select>
    <select id="queryDemandManagementByConditionIds" resultType="com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementVo">
        <!-- Started by AICoder, pid:d5cc9e7e6fa40bd1420609232076b8006466da73 -->
        select *
        from demand_management
        where project_id = #{projectId}
        and product_category_id = #{productCategoryId}
    </select>

    <!-- Ended by AICoder, pid:d5cc9e7e6fa40bd1420609232076b8006466da73 -->


    <!-- Started by AICoder, pid:o0a31i78f694e0e1441f0a3e214ad21c32b30e73 -->

        <!-- 根据产品类别ID和时间统计按天的需求标书 -->
        <select id="demandLectotypeStatDay" parameterType="string"
                resultType="com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo">
            <!-- 查询总标书数量、普通标书数量和指定标书数量 -->
            SELECT
            a.allLectotypeNum,
            b.bidLectotypeNum,
            c.appointLectotypeNum
            FROM
            (select count(*) as allLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMMDD') = #{time}  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) a,
            ( select count(*) as bidLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMMDD') = #{time} and lectotype_type ='1'  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) b,
            ( select count(*) as appointLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMMDD') = #{time} and lectotype_type ='2'   and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) c
        </select>

        <!-- 根据产品类别ID和周范围统计需求标书 -->
        <select id="demandLectotypeStatWeek" parameterType="string"
                resultType="com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo">
            <!-- 查询总标书数量、普通标书数量和指定标书数量 -->
            SELECT
            a.allLectotypeNum,
            b.bidLectotypeNum,
            c.appointLectotypeNum
            FROM
            (select count(*) as allLectotypeNum from demand_management_lectotype
            where create_time>=#{monday} and  create_time &lt;= #{sunday}
            and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) a,
            ( select count(*) as bidLectotypeNum from demand_management_lectotype
            where  create_time>=#{monday} and  create_time &lt;= #{sunday}
            and lectotype_type ='1'  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) b,
            ( select count(*) as appointLectotypeNum from demand_management_lectotype
            where   create_time>=#{monday} and  create_time &lt;= #{sunday}
            and lectotype_type ='2'   and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) c
        </select>

        <!-- 根据产品类别ID和时间统计按月的需求标书 -->
        <select id="demandLectotypeStatMonth" parameterType="string"
                resultType="com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo">
            <!-- 查询总标书数量、普通标书数量和指定标书数量 -->
            SELECT
            a.allLectotypeNum,
            b.bidLectotypeNum,
            c.appointLectotypeNum
            FROM
            (select count(*) as allLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMM') = #{time}  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) a,
            ( select count(*) as bidLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMM') = #{time} and lectotype_type ='1'  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) b,
            ( select count(*) as appointLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYYMM') = #{time} and lectotype_type ='2'   and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) c
        </select>

        <!-- 根据产品类别ID和时间统计按年的需求标书 -->
        <select id="demandLectotypeStatYear" parameterType="string"
                resultType="com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo">
            <!-- 查询总标书数量、普通标书数量和指定标书数量 -->
            SELECT
            a.allLectotypeNum,
            b.bidLectotypeNum,
            c.appointLectotypeNum
            FROM
            (select count(*) as allLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY') = #{time}  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) a,
            ( select count(*) as bidLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY') = #{time} and lectotype_type ='1'  and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) b,
            ( select count(*) as appointLectotypeNum from demand_management_lectotype
            where TO_CHAR(TO_TIMESTAMP(create_time, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY') = #{time} and lectotype_type ='2'   and demand_id in (
            select id from demand_management where  product_category_id = #{productCategoryId}
            )) c
        </select>

        <!-- 统计所有需求标书 -->
        <select id="demandLectotypeStatAll"
                resultType="com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo">
            <!-- 查询总标书数量、普通标书数量和指定标书数量 -->
            SELECT
            a.allLectotypeNum,
            b.bidLectotypeNum,
            c.appointLectotypeNum
            FROM
            (select count(*) as allLectotypeNum from demand_management_lectotype ) a,
            ( select count(*) as bidLectotypeNum from demand_management_lectotype
            where lectotype_type ='1' ) b,
            ( select count(*) as appointLectotypeNum from demand_management_lectotype
            where lectotype_type ='2' ) c
        </select>
    <!-- Ended by AICoder, pid:o0a31i78f694e0e1441f0a3e214ad21c32b30e73 -->

</mapper>