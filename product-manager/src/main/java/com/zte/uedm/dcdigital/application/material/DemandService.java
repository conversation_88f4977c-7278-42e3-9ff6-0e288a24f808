package com.zte.uedm.dcdigital.application.material;

/* Started by AICoder, pid:l2d353a5357b18114de7094e603f181f3036fd42 */

import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerVo;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DemandManagementPo;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementLectotypeDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.LectotypeMaterialAddDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.PageDemandDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import com.zte.uedm.dcdigital.sdk.project.vo.BillOfQuantityVo;

import java.util.List;

/**
 * DemandService 接口定义了需求管理服务的方法。
 *
 * <AUTHOR>
 */
public interface DemandService {

    /**
     * 添加新的需求管理记录。
     *
     * @param demandManagementDto 包含需求管理信息的数据传输对象
     */
    void addDemand(DemandManagementDto demandManagementDto);

    /* Started by AICoder, pid:05ac4xdad51a36914bd809b9e07a838a7b682b64 */
    /**
     * 分页查询需求管理记录。
     *
     * @param pageDemandDto 包含分页和过滤条件的数据传输对象
     * @return 符合条件的需求管理记录列表
     */
    PageVO<DemandManagementVo> pageDemand(PageDemandDto pageDemandDto);

    /**
     * 分页查询物料清单记录。
     *
     * @param demandId 需求ID
     * @param page 当前页码
     * @param size 每页大小
     * @return 符合条件的物料清单记录列表
     */
    PageVO<BillOfQuantityVo> pageQuantity(String demandId, Integer page, Integer size);

    /**
     * 分页查询定标记录。
     *
     * @param demandId 需求ID
     * @param page 当前页码
     * @param size 每页大小
     * @return 符合条件的定标记录列表
     */
    PageVO<DemandManagementLectotypeVo> pageLectotype(String demandId, Integer page, Integer size);

    /**
     * 查询需求管理记录的详细信息。
     *
     * @param demandId 需求ID
     * @return 需求管理记录的详细信息
     */
    DemandManagementPo detailDemand(String demandId);

    /**
     * 查询定标记录的详细信息，包括物料信息。
     *
     * @param lectotypeId 定标ID
     * @param page 当前页码
     * @param size 每页大小
     * @return 定标记录的详细信息
     */
    PageVO<MaterialDetailVo> detailDemandLectotypeMaterial(String lectotypeId, Integer page, Integer size);

    /**
     * 添加新的定标记录。
     *
     * @param dto 包含定标信息的数据传输对象
     */
    void addLectotype(DemandManagementLectotypeDto dto);

    /**
     * 更新定标记录。
     *
     * @param dto 包含更新信息的数据传输对象
     */
    void updLectotype(DemandManagementLectotypeUpdDto dto);
    void updLectotypeStatus(DemandManagementLectotypeUpdDto dto);

    /**
     * 删除指定的定标记录。
     *
     * @param lectotypeId 定标ID
     */
    void delLectotype(String lectotypeId);

    /**
     * 批量删除定标记录。
     *
     * @param lectotypeIds 以逗号分隔的定标ID列表
     */
    void delLectotypes(String lectotypeIds);

    /**
     * 添加新的定标物料记录。
     *
     * @param lectotypeMaterialAddDto 包含定标物料信息的数据传输对象
     */
    void addLectotypeMaterial(LectotypeMaterialAddDto lectotypeMaterialAddDto);

    /**
     * 删除指定的定标物料记录。
     *
     * @param lectotypeId 定标ID
     * @param materialIds 以逗号分隔的物料ID列表
     */
    void delLectotypeMaterial(String lectotypeId, String materialIds);

    List<IdNameBean> queryAllStatus();

    SelectionFormDetailsVo querySelectionFormById(String id);

    void editLectotype(DemandManagementLectotypeDto dto);
    /* Ended by AICoder, pid:05ac4xdad51a36914bd809b9e07a838a7b682b64 */

    void updLectotypeMaterialStatus(String materialId, Integer type);

    DemandManagementInnerVo getDemandManagement(DemandManagementInnerDto innerDto);

    void updateDemand(DemandManagementDto managementDto);
    void onceSubmit(String lectotypeId);
    DemandAndLectotypeDetailsVo getDemandManagementLectotypeById(String lectotypeId);

    void updateDemandManagementInfoAndLecToTypeByCondition(DemandManagementInnerDto innerDto);

    List<DemandManagementInnerVo> getDemandManagementList(DemandManagementInnerDto managementInnerDto);


    /* Started by AICoder, pid:x9545xef78d8fce1495c0aabf0bb93108078bab3 */
    /**
     * 根据产品类别ID、时间和类型统计需求标书。
     *
     * @param productCategoryId 产品类别ID
     * @param time              统计时间（格式：YYYY-MM-DD）
     * @param type              统计类型（例如：1-按天，2-按周，3-按月，4-按年）
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStat(String productCategoryId, String time, Integer type);

    /* Ended by AICoder, pid:cc23d45800y22ee144c40a4300b096072cc8a372 */

    /**
     * 统计所有需求标书。
     *
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStatAll();
    /* Ended by AICoder, pid:x9545xef78d8fce1495c0aabf0bb93108078bab3 */
}
/* Ended by AICoder, pid:l2d353a5357b18114de7094e603f181f3036fd42 */
