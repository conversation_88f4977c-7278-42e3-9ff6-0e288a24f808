package com.zte.uedm.dcdigital.infrastructure.repository.mapper;

/* Started by AICoder, pid:y37b47ad5d3563e14ae50b2ea036eb16091857c4 */
import com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DemandManagementPo;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementLectotypeDto;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.PageDemandDto;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementLectotypeVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.DemandManagementVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.LectotypeMaterialVo;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.SelectionFormVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DemandMapper 接口用于定义与需求管理相关的数据库操作。
 *
 * <AUTHOR>
 */
@Mapper
public interface DemandMapper {

    /**
     * addDemand 方法用于向数据库中添加新的需求管理记录。
     *
     * @param demandManagementDto 包含需求管理信息的数据传输对象
     */
    void addDemand(DemandManagementDto demandManagementDto);
    void updDemand(DemandManagementPo demandManagementPo);

    /* Started by AICoder, pid:3911ajfe98p78b114766081ee05b3f2e94451e73 */
    /**
     * 分页查询需求管理记录。
     *
     * @param pageDemandDto 包含分页和过滤条件的数据传输对象
     * @return 符合条件的需求管理记录列表
     */
    List<DemandManagementVo> pageDemand(PageDemandDto pageDemandDto);

    /**
     * 添加新的定标物料记录。
     *
     * @param lectotypeId 定标ID
     * @param materialId 物料ID
     */
    void addLectotypeMaterial(@Param("lectotypeId") String lectotypeId,
                              @Param("materialId") String materialId,
                              @Param("createTime") String createTime);

    /**
     * 删除指定的定标物料记录。
     *
     * @param lectotypeId 定标ID
     * @param materialId 物料ID
     */
    void delLectotypeMaterial(@Param("lectotypeId") String lectotypeId,
                              @Param("materialId") String materialId);

    List<String> getLectotypeMaterialByLtc(@Param("lectotypeId") String lectotypeId);

    /* Ended by AICoder, pid:3911ajfe98p78b114766081ee05b3f2e94451e73 */

    /* Started by AICoder, pid:4377b6f2c1j3611142cb087ac0d75d504eb034e0 */
    /**
     * 根据定标ID删除定标物料记录。
     *
     * @param lectotypeId 定标ID
     */
    void delLectotypeMaterialByLec(@Param("lectotypeId") String lectotypeId);

    /**
     * 根据物料ID删除定标物料记录。
     *
     * @param materialId 物料ID
     */
    void delLectotypeMaterialByMat(@Param("materialId") String materialId);

    /**
     * 删除指定的定标记录。
     *
     * @param lectotypeId 定标ID
     */
    void delLectotype(@Param("lectotypeId")String lectotypeId);

    /**
     * 添加新的定标记录。
     *
     * @param dto 包含定标信息的数据传输对象
     */
    void addLectotype(DemandManagementLectotypeDto dto);

    /**
     * 更新定标记录。
     *
     * @param dto 包含更新信息的数据传输对象
     */
    void updLectotype(DemandManagementLectotypeUpdDto dto);
    void updLectotypeStatus(DemandManagementLectotypeUpdDto dto);
    DemandManagementLectotypeVo getLectotypeById(@Param("lectotypeId")String lectotypeId);

    /**
     * 分页查询定标记录。
     *
     * @param demandId 需求ID
     * @return 符合条件的定标记录列表
     */
    List<DemandManagementLectotypeVo> pageLectotype(@Param("demandId") String demandId);

    /**
     * 根据需求ID查询需求管理记录。
     *
     * @param demandId 需求ID
     * @return 需求管理记录
     */
    DemandManagementPo selectDemandById(@Param("demandId") String demandId);

    boolean doLectotypeExist(@Param("id")String id);

    List<SelectionFormVo> queryByCondition(@Param("projectIds") List<String> projectIds, @Param("selectionName") String selectionName,
                                           @Param("statusList") List<Integer> statusList, @Param("productCategoryIdList") List<String> productCategoryIdList,
                                           @Param("typeList") List<String> typeList);

    List<LectotypeMaterialVo> getLectotypeMaterialByMaterialIds( @Param("ids") List<String> ids);
    /* Ended by AICoder, pid:4377b6f2c1j3611142cb087ac0d75d504eb034e0 */


    DemandManagementLectotypeVo getLectotypeByMaterialId(@Param("materialId")String materialId);

    void batchDelLectotypeMaterial( @Param("lectotypeId") String lectotypeId, @Param("materialIdList") List<String> materialIdList);

    String getProductIdByMaterialId(@Param("materialId")String materialId);

    DemandManagementPo getDemandManagementLectotypeById(@Param("lectotypeId")String lectotypeId);

    DemandManagementVo queryDemandManagement(@Param("projectId") String projectId, @Param("productCategoryId") String productCategoryId);

    DemandManagementLectotypeVo queryEarliestByDemandId(@Param("id") String id,@Param("type") String type);

    List<DemandManagementVo> queryDemandByProjectAndCategoryIds(@Param("projectId") String projectId, @Param("categoryIds") List<String> categoryIds);

    List<DemandManagementLectotypeVo> queryLectotypeByDemandIdList(@Param("demandIds")List<String> demandIds, @Param("type") String type);

    DemandManagementLectotypeVo queryLastByDemandId(@Param("id")String id);

    List<LectotypeMaterialVo> queryRelationMaterialByIds(@Param("lectotypeIds") List<String> lectotypeIds);

    List<LectotypeMaterialVo> queryRelationMaterialByIds(@Param("projectId") String projectId, @Param("productCategoryId") String productCategoryId);

    List<DemandManagementVo> queryDemandManagementByConditionIds(@Param("projectId") String projectId, @Param("productCategoryId") String productCategoryId);

    void updLectotypeByDemandId(DemandManagementLectotypeUpdDto lectotypeUpdDto);

    /* Started by AICoder, pid:ed331xad35k6a891440f0aab807046571292ede8 */
    /**
     * 根据产品类别ID和时间统计按天的需求标书。
     *
     * @param productCategoryId 产品类别ID
     * @param time              统计时间（格式：YYYY-MM-DD）
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStatDay(
            @Param("productCategoryId") String productCategoryId,
            @Param("time") String time);

    /**
     * 根据产品类别ID和周范围统计需求标书。
     *
     * @param productCategoryId 产品类别ID
     * @param monday            周一日期（格式：YYYY-MM-DD）
     * @param sunday            周日日期（格式：YYYY-MM-DD）
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStatWeek(
            @Param("productCategoryId") String productCategoryId,
            @Param("monday") String monday,
            @Param("sunday") String sunday);

    /**
     * 根据产品类别ID和时间统计按月的需求标书。
     *
     * @param productCategoryId 产品类别ID
     * @param time              统计时间（格式：YYYY-MM）
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStatMonth(
            @Param("productCategoryId") String productCategoryId,
            @Param("time") String time);

    /**
     * 根据产品类别ID和时间统计按年的需求标书。
     *
     * @param productCategoryId 产品类别ID
     * @param time              统计时间（格式：YYYY）
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStatYear(
            @Param("productCategoryId") String productCategoryId,
            @Param("time") String time);

    /**
     * 统计所有需求标书。
     *
     * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
     */
    BusinessLectotypeStatisticVo demandLectotypeStatAll();
    /* Ended by AICoder, pid:ed331xad35k6a891440f0aab807046571292ede8 */
}
/* Ended by AICoder, pid:y37b47ad5d3563e14ae50b2ea036eb16091857c4 */