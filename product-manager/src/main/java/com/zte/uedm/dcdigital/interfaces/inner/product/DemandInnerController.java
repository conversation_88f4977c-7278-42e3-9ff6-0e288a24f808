/* Started by AICoder, pid:cf0c0y30e1l4994144c50a6060b96e47d400c79e */
package com.zte.uedm.dcdigital.interfaces.inner.product;

import com.zte.uedm.dcdigital.application.material.DemandService;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerVo;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto;
import com.zte.uedm.dcdigital.common.web.BaseResult;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.DemandManagementDto;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@Controller
@Path("/demand-inner")
@Slf4j
public class DemandInnerController {

    @Autowired
    private DemandService demandService;

    /**
     * 添加新的需求管理记录。
     *
     * @param managementInnerDto 包含需求管理信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/add-demand")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "需求新增", notes = "需求新增", httpMethod = "POST")
    public BaseResult<Object> addDemandInner(DemandManagementInnerDto managementInnerDto) {
        log.info("addDemandInner: {}", managementInnerDto);
        DemandManagementDto managementDto=new DemandManagementDto();
        BeanUtils.copyProperties(managementInnerDto,managementDto);
        demandService.addDemand(managementDto);
        return BaseResult.success();
    }

    /**
     * 更新需求管理徐昂行记录。
     *
     * @param managementInnerDto 包含需求管理信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/update-demand-by-id")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "需求选型更新", notes = "需求选型更新", httpMethod = "POST")
    public BaseResult<Object> updateDemandInner(DemandManagementInnerDto managementInnerDto) {
        log.info("updateDemandInner: {}", managementInnerDto);
        DemandManagementDto managementDto=new DemandManagementDto();
        BeanUtils.copyProperties(managementInnerDto,managementDto);
        demandService.updateDemand(managementDto);
        return BaseResult.success();
    }


    /**
     * 查询需求管理记录是否已存在。
     *
     * @param managementInnerDto 包含需求管理信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/get-demand")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "需求查询", notes = "需求查询", httpMethod = "POST")
    public BaseResult<Object> getDemandManagement(DemandManagementInnerDto managementInnerDto) {
        log.info("getDemandManagement: {}", managementInnerDto);
        DemandManagementInnerVo demandManagementInnerVo=demandService.getDemandManagement(managementInnerDto);
        return BaseResult.success(demandManagementInnerVo);
    }
    /* Started by AICoder, pid:77fb6a8be5ua82f14bc308e380768d1aa736825b */
    /**
     * 查询需求管理记录列表。
     *
     * @param managementInnerDto 包含需求管理信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/get-demand-list")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "需求列表查询", notes = "需求列表查询", httpMethod = "POST")
    public BaseResult<Object> getDemandManagementList(DemandManagementInnerDto managementInnerDto) {
        log.info("getDemandManagementList: {}", managementInnerDto);
        List<DemandManagementInnerVo> demandManagementInnerVo = demandService.getDemandManagementList(managementInnerDto);
        return BaseResult.success(demandManagementInnerVo);
    }

    /* Ended by AICoder, pid:77fb6a8be5ua82f14bc308e380768d1aa736825b */

    /**
     * 编辑选型记录。
     *
     * @param dto 包含更新信息的数据传输对象
     * @return 操作结果
     */
    @POST
    @Path("/updLectotype")
    @Produces({MediaType.APPLICATION_JSON})
    @Consumes(MediaType.APPLICATION_JSON)
    @ApiOperation(value = "编辑选型", notes = "编辑选型", httpMethod = "POST")
    public BaseResult<Object> updLectotype(DemandManagementLectotypeUpdDto dto) {
        demandService.updLectotypeStatus(dto);
        return BaseResult.success();
    }


    /* Started by AICoder, pid:s04f2x03c45105714b6b0a9360940b1623847a7c */
    /**
     * 更新需求单与选型单的状态为"已取消"。
     *
     * @param innerDto 包含待更新的需求单传输对象
     * @return 操作结果
     */
    @POST
    @Path("/update-demand-lect-by-condition")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public BaseResult<Object> updateDemandManagementInfoAndLecToTypeByCondition(DemandManagementInnerDto innerDto) {
        demandService.updateDemandManagementInfoAndLecToTypeByCondition(innerDto);
        return BaseResult.success(); // 返回成功结果
    }

    /* Ended by AICoder, pid:s04f2x03c45105714b6b0a9360940b1623847a7c */

    /* Started by AICoder, pid:b7161decd3fb5e814cfc09d61042fb35f671513b */
    @POST
    @Path("/demandLectotypeStat")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public BaseResult<Object> demandLectotypeStat(
            @QueryParam("productCategoryId") String productCategoryId,
            @QueryParam("time") String time,
            @QueryParam("type") Integer type) {
        /**
         * 根据产品类别ID、时间和类型统计需求标书。
         *
         * @param productCategoryId 产品类别ID
         * @param time              统计时间（格式：YYYY-MM-DD）
         * @param type              统计类型（例如：1-按天，2-按周，3-按月，4-按年）
         * @return 包含标书统计结果的 BaseResult 对象
         */
        return BaseResult.success(demandService.demandLectotypeStat(productCategoryId, time, type)); // 返回成功结果
    }

    @POST
    @Path("/demandLectotypeStatAll")
    @Produces(MediaType.APPLICATION_JSON)
    @Consumes(MediaType.APPLICATION_JSON)
    public BaseResult<Object> demandLectotypeStatAll() {
        /**
         * 统计所有需求标书。
         *
         * @return 包含标书统计结果的 BaseResult 对象
         */
        return BaseResult.success(demandService.demandLectotypeStatAll()); // 返回成功结果
    }
    /* Ended by AICoder, pid:b7161decd3fb5e814cfc09d61042fb35f671513b */
}

/* Ended by AICoder, pid:cf0c0y30e1l4994144c50a6060b96e47d400c79e */